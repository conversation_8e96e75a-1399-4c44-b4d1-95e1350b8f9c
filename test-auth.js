/**
 * Authentication Test Script
 * Test authentication system independently for debugging
 */

const AuthService = require('./services/auth');
const logger = require('./utils/logger');

async function testAuthentication() {
  logger.info('🧪 Testing Authentication System...');
  
  const auth = new AuthService();
  
  try {
    // Test 1: Token Generation
    logger.info('📝 Test 1: Token Generation');
    await auth.generateToken();
    logger.success(`✅ Token generated: ${auth.token ? auth.token.substring(0, 20) + '...' : 'null'}`);
    logger.info(`   Expiry: ${auth.tokenExpiry}`);
    
    // Test 2: Login Status Check
    logger.info('📝 Test 2: Login Status Check');
    const loginStatus = await auth.checkLoginStatus();
    logger.info(`   Login Status: ${loginStatus}`);
    
    // Test 3: User Profile
    logger.info('📝 Test 3: User Profile');
    try {
      const profile = await auth.getUserProfile();
      logger.success(`✅ Profile loaded: ${profile.fullname} (${profile.email})`);
      logger.info(`   Tier: ${profile.tier_id} | Status: ${profile.status_tier}`);
    } catch (profileError) {
      logger.error(`❌ Profile failed: ${profileError.message}`);
    }
    
    // Test 4: Loyalty Info
    logger.info('📝 Test 4: Loyalty Info');
    try {
      const loyalty = await auth.getLoyaltyInfo();
      logger.success(`✅ Loyalty loaded: ${loyalty.fist_name}`);
      logger.info(`   EXP: ${loyalty.accumulated_exp} | Daily: ${loyalty.daily_exp}/${loyalty.max_daily_exp}`);
      logger.info(`   Tier: ${loyalty.current_tier} → ${loyalty.next_tier}`);
    } catch (loyaltyError) {
      logger.error(`❌ Loyalty failed: ${loyaltyError.message}`);
    }
    
    // Test 5: Full Initialize
    logger.info('📝 Test 5: Full Initialize');
    const auth2 = new AuthService();
    try {
      await auth2.initialize();
      logger.success('✅ Full initialization successful');
    } catch (initError) {
      logger.error(`❌ Full initialization failed: ${initError.message}`);
    }
    
    logger.success('🎉 Authentication testing completed');
    
  } catch (error) {
    logger.error('💥 Authentication test failed', error.message);
    
    // Additional debugging info
    logger.info('🔍 Debug Information:');
    logger.info(`   Token: ${auth.token ? 'Present' : 'Missing'}`);
    logger.info(`   Token Length: ${auth.token ? auth.token.length : 0}`);
    logger.info(`   Is Expired: ${auth.isTokenExpired()}`);
    logger.info(`   Is Logged In: ${auth.isLoggedIn}`);
    
    if (error.response) {
      logger.debug('Error Response:', error.response.data);
    }
  }
}

// Run test
testAuthentication().catch(error => {
  logger.error('💥 Test script failed', error.message);
  process.exit(1);
});
