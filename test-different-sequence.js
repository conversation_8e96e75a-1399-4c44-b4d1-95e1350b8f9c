/**
 * Test Different Tracking Sequences
 * Try different tracking sequences to find the one that awards points
 */

const axios = require('axios');

const config_file = require('./config');
const WORKING_TOKEN = config_file.AUTH.WORKING_TOKEN;

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
  }
};

async function testDifferentSequences() {
  console.log('🧪 Testing Different Tracking Sequences');
  console.log('=======================================');
  
  try {
    // Get initial points
    const getPoints = async () => {
      const response = await axios.post(`${config.API.BASE_URL}/api/fs/feed/ub-point`, {
        content_type: ''
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      return response.data.data.point;
    };
    
    const initialPoints = await getPoints();
    console.log(`💰 Initial points: ${initialPoints}`);
    
    // Get fresh unlimited content
    const contentResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
      is_regular: 1,
      campaign: '',
      category: '',
      sub_category: 'daily-life',
      is_branded: 1,
      format: '',
      is_highlight: '',
      pinned: '',
      page: 0,
      length: 10,
      is_engagement: '',
      string_id: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    const unlimitedContent = contentResponse.data.data.items.filter(item => 
      item.is_exclude_daily_limit === 1 && 
      item.finished !== 1
    );
    
    if (unlimitedContent.length === 0) {
      console.log('❌ No unlimited content available');
      return;
    }
    
    const testItem = unlimitedContent[0];
    console.log(`\n📄 Testing: ${testItem.title}`);
    console.log(`   Points: ${testItem.point}`);
    console.log(`   Campaign: ${testItem.campaign_string_id}`);
    
    // Test Sequence 1: With user_id parameter
    console.log('\n🧪 Sequence 1: With user_id parameter');
    
    try {
      // View with user_id
      await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
        user_id: 'doni_setiawan_123', // Try with user identifier
        event: 'general_event',
        campaign: testItem.campaign_string_id,
        event_category: 'on view article',
        event_action: 'on view article',
        event_label: testItem.title,
        creative: testItem.href,
        content_id: parseInt(testItem.id)
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Completion with user_id
      await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
        user_id: 'doni_setiawan_123',
        event: 'general_event',
        campaign: testItem.campaign_string_id,
        event_category: 'page_reached_finish_on_read_article',
        event_action: 'simple engagement',
        event_label: testItem.title,
        creative: testItem.href,
        content_id: parseInt(testItem.id)
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      const points1 = await getPoints();
      console.log(`   Points after sequence 1: ${points1} (earned: ${points1 - initialPoints})`);
      
    } catch (error) {
      console.log(`   ❌ Sequence 1 failed: ${error.message}`);
    }
    
    // Test Sequence 2: Different event structure
    console.log('\n🧪 Sequence 2: Different event structure');
    
    const testItem2 = unlimitedContent[1] || unlimitedContent[0];
    
    try {
      // Start event
      await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
        user_id: false,
        event: 'start_reading',
        campaign: testItem2.campaign_string_id,
        event_category: 'article_interaction',
        event_action: 'start',
        event_label: testItem2.title,
        creative: testItem2.href,
        content_id: parseInt(testItem2.id)
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // End event
      await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
        user_id: false,
        event: 'finish_reading',
        campaign: testItem2.campaign_string_id,
        event_category: 'article_interaction',
        event_action: 'finish',
        event_label: testItem2.title,
        creative: testItem2.href,
        content_id: parseInt(testItem2.id)
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      const points2 = await getPoints();
      console.log(`   Points after sequence 2: ${points2} (earned: ${points2 - initialPoints})`);
      
    } catch (error) {
      console.log(`   ❌ Sequence 2 failed: ${error.message}`);
    }
    
    // Test Sequence 3: Minimal tracking
    console.log('\n🧪 Sequence 3: Minimal tracking');
    
    const testItem3 = unlimitedContent[2] || unlimitedContent[0];
    
    try {
      // Just completion event
      await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
        user_id: false,
        event: 'general_event',
        campaign: testItem3.campaign_string_id,
        event_category: 'page_reached_finish_on_read_article',
        event_action: 'simple engagement',
        event_label: testItem3.title,
        creative: testItem3.href,
        content_id: parseInt(testItem3.id)
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      const points3 = await getPoints();
      console.log(`   Points after sequence 3: ${points3} (earned: ${points3 - initialPoints})`);
      
    } catch (error) {
      console.log(`   ❌ Sequence 3 failed: ${error.message}`);
    }
    
    // Check if any content is now finished
    console.log('\n📊 Checking content status...');
    
    const updatedContentResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
      is_regular: 1,
      campaign: '',
      category: '',
      sub_category: 'daily-life',
      is_branded: 1,
      format: '',
      is_highlight: '',
      pinned: '',
      page: 0,
      length: 10,
      is_engagement: '',
      string_id: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    const updatedItems = updatedContentResponse.data.data.items;
    
    [testItem, testItem2, testItem3].forEach((item, index) => {
      const updated = updatedItems.find(u => u.id === item.id);
      if (updated) {
        console.log(`   Item ${index + 1}: ${updated.title}`);
        console.log(`     Finished: ${item.finished} → ${updated.finished}`);
      }
    });
    
    const finalPoints = await getPoints();
    console.log(`\n💰 Final points: ${finalPoints}`);
    console.log(`🎯 Total earned: ${finalPoints - initialPoints}`);
    
    if (finalPoints > initialPoints) {
      console.log('🎉 SUCCESS! Points were earned!');
    } else {
      console.log('❌ No points earned from any sequence');
      console.log('💡 Possible reasons:');
      console.log('   - Content already processed by this account');
      console.log('   - Points require longer delay');
      console.log('   - Different tracking sequence needed');
      console.log('   - Account/session specific requirements');
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log('Error data:', error.response.data);
    }
  }
}

testDifferentSequences();
