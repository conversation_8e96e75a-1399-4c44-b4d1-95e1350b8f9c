/**
 * Test Working Token
 * Test the working token found by bypass script
 */

const axios = require('axios');

const WORKING_TOKEN = 'dJJH2C7WSUZ9Cg3gOqrsPskXQWyzCWbG';

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  }
};

async function testWorkingToken() {
  console.log('🧪 Testing Working Token');
  console.log('========================');
  console.log(`Token: ${WORKING_TOKEN.substring(0, 20)}...`);
  
  try {
    // Test 1: Login Status
    console.log('\n1. Testing login status...');
    
    const loginResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/login-status`, {}, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    console.log(`✅ Login Status: ${loginResponse.data.data.data.login_status}`);
    
    // Test 2: User Profile
    console.log('\n2. Testing user profile...');
    
    const profileResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/get-profile`, {}, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (profileResponse.data.code === 200) {
      const profile = profileResponse.data.data.data;
      console.log(`✅ Profile: ${profile.fullname} (${profile.email})`);
      console.log(`   Tier: ${profile.tier_id} | Status: ${profile.status_tier}`);
    }
    
    // Test 3: Loyalty Info
    console.log('\n3. Testing loyalty info...');
    
    const loyaltyResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/loyalty-teaser`, {}, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (loyaltyResponse.data.code === 200) {
      const loyalty = loyaltyResponse.data.data.data;
      console.log(`✅ Loyalty: ${loyalty.fist_name}`);
      console.log(`   EXP: ${loyalty.accumulated_exp} | Daily: ${loyalty.daily_exp}/${loyalty.max_daily_exp}`);
      console.log(`   Tier: ${loyalty.current_tier} → ${loyalty.next_tier}`);
    }
    
    // Test 4: Content Access
    console.log('\n4. Testing content access...');
    
    const contentResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
      is_regular: 1,
      campaign: '',
      category: '',
      sub_category: 'music',
      is_branded: 1,
      format: '',
      is_highlight: '',
      pinned: '',
      page: 0,
      length: 10,
      is_engagement: '',
      string_id: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (contentResponse.data.code === 200) {
      const items = contentResponse.data.data.items;
      console.log(`✅ Content access: ${items.length} items found`);
      
      // Show first few items
      items.slice(0, 3).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.title} (${item.format}, ${item.point} points)`);
      });
    }
    
    // Test 5: Points Check
    console.log('\n5. Testing points check...');
    
    const pointsResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/ub-point`, {
      content_type: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (pointsResponse.data.code === 200) {
      console.log(`✅ Current Points: ${pointsResponse.data.data.point}`);
    }
    
    // Test 6: Interaction Tracking
    console.log('\n6. Testing interaction tracking...');
    
    const trackingResponse = await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
      user_id: false,
      event: 'general_event',
      campaign: 'test',
      event_category: 'test',
      event_action: 'test',
      event_label: 'test',
      creative: 'https://amild.id/'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (trackingResponse.data.code === 200) {
      console.log('✅ Interaction tracking: Working');
    }
    
    console.log('\n🎉 All tests passed! Token is fully functional.');
    console.log('💡 Bot should work with this token.');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log('Error status:', error.response.status);
      console.log('Error data:', error.response.data);
    }
  }
}

testWorkingToken();
