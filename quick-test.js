/**
 * Quick Test Script
 * Simple test to check if authentication works
 */

const axios = require('axios');

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  },
  AUTH: {
    NAME: 'amild',
    SECRET_KEY: '8Q1kUKMlUZUsCntnclnPF+DDQ+k1HY0b1UNGesHK',
    APP_KEY: '629DE67FBE4A67B65541',
    DEVICE_ID: 'LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL',
    DEVICE_TYPE: 'website'
  }
};

async function quickTest() {
  console.log('🧪 Quick Authentication Test');
  console.log('============================');
  
  try {
    // Step 1: Generate Token
    console.log('1. Generating token...');
    
    const tokenResponse = await axios.post(`${config.API.BASE_URL}/api/token/get`, {
      name: config.AUTH.NAME,
      secret_key: config.AUTH.SECRET_KEY,
      device_type: config.AUTH.DEVICE_TYPE,
      app_key: config.AUTH.APP_KEY,
      device_id: config.AUTH.DEVICE_ID
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    console.log('✅ Token Response:', tokenResponse.data);
    
    if (tokenResponse.data.code !== 200) {
      throw new Error('Token generation failed');
    }
    
    const token = tokenResponse.data.data.token.token_code;
    console.log(`✅ Token: ${token.substring(0, 20)}...`);
    
    // Step 2: Check Login Status
    console.log('\n2. Checking login status...');
    
    const loginResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/login-status`, {}, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': token,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    console.log('✅ Login Response:', loginResponse.data);
    
    const isLoggedIn = loginResponse.data.data.data.login_status;
    console.log(`Login Status: ${isLoggedIn}`);
    
    // Step 3: Get Profile (if logged in or force try)
    console.log('\n3. Getting user profile...');
    
    try {
      const profileResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/get-profile`, {}, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': token,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      console.log('✅ Profile Response:', profileResponse.data);
      
      if (profileResponse.data.code === 200) {
        const profile = profileResponse.data.data.data;
        console.log(`✅ Profile: ${profile.fullname} (${profile.email})`);
        console.log(`   Tier: ${profile.tier_id} | Status: ${profile.status_tier}`);
      }
    } catch (profileError) {
      console.log('❌ Profile Error:', profileError.response?.data || profileError.message);
    }
    
    // Step 4: Get Loyalty Info
    console.log('\n4. Getting loyalty info...');
    
    try {
      const loyaltyResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/loyalty-teaser`, {}, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': token,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      console.log('✅ Loyalty Response:', loyaltyResponse.data);
      
      if (loyaltyResponse.data.code === 200) {
        const loyalty = loyaltyResponse.data.data.data;
        console.log(`✅ Loyalty: ${loyalty.fist_name}`);
        console.log(`   EXP: ${loyalty.accumulated_exp} | Daily: ${loyalty.daily_exp}/${loyalty.max_daily_exp}`);
        console.log(`   Tier: ${loyalty.current_tier} → ${loyalty.next_tier}`);
      }
    } catch (loyaltyError) {
      console.log('❌ Loyalty Error:', loyaltyError.response?.data || loyaltyError.message);
    }
    
    console.log('\n🎉 Quick test completed!');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log('Error response:', error.response.data);
    }
  }
}

quickTest();
