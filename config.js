/**
 * Amild Bot Configuration
 * <PERSON>risi semua konfigurasi dan konstanta yang diperlukan bot
 */

module.exports = {
  // API Configuration
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  },

  // Authentication Credentials
  AUTH: {
    NAME: 'amild',
    SECRET_KEY: '8Q1kUKMlUZUsCntnclnPF+DDQ+k1HY0b1UNGesHK',
    APP_KEY: '629DE67FBE4A67B65541',
    DEVICE_ID: 'meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r',
    DEVICE_TYPE: 'website',
    // Working token from bypass (expires: check token expiry)
    WORKING_TOKEN: 'r5rMPe5AYTQP2HuftKv4lu0p9c6JpkEa'
  },

  // Content Priority Configuration
  PRIORITY: {
    // Campaign priorities (higher = better)
    CAMPAIGNS: {
      'gac-2024': { priority: 5, expectedPoints: 150 },
      'inspiraksi2025': { priority: 4, expectedPoints: 100 },
      'go-ahead-music': { priority: 4, expectedPoints: 100 },
      'why-not': { priority: 3, expectedPoints: 100 },
      'gac2024': { priority: 2, expectedPoints: 50 },
      'avolution-twilight-breeze': { priority: 2, expectedPoints: 50 },
      'uncompromising-a': { priority: 1, expectedPoints: 50 }
    },

    // Category priorities
    CATEGORIES: {
      'music': { priority: 5, expectedItems: 32 },
      'lifestyle': { priority: 4, expectedItems: 8 },
      'daily-life': { priority: 3, expectedItems: 15 },
      'travel': { priority: 3, expectedItems: 9 },
      'art': { priority: 2, expectedItems: 5 },
      'sustainability': { priority: 2, expectedItems: 4 }
    },

    // Content type priorities
    CONTENT_TYPES: {
      'video': { priority: 5, basePoints: 150 },
      'article': { priority: 3, basePoints: 100 },
      'carousel': { priority: 4, basePoints: 100 }
    }
  },

  // Timing Configuration (in milliseconds)
  TIMING: {
    // Reading simulation
    ARTICLE_READ_TIME_PER_CHAR: 50, // 50ms per character
    MIN_ARTICLE_READ_TIME: 10000, // 10 seconds minimum
    MAX_ARTICLE_READ_TIME: 120000, // 2 minutes maximum
    
    // Video simulation  
    VIDEO_WATCH_MULTIPLIER: 1.0, // Watch full duration
    
    // Carousel simulation
    CAROUSEL_COMPLETE_TIME: 5000, // 5 seconds
    
    // Tracking intervals
    SCROLL_TRACKING_INTERVAL: 3000, // Send scroll event every 3 seconds
    LOGIN_CHECK_INTERVAL: 120000, // Check login every 2 minutes
    
    // Request delays
    REQUEST_DELAY_MIN: 1000, // 1 second minimum between requests
    REQUEST_DELAY_MAX: 3000, // 3 seconds maximum between requests
    
    // Error handling
    RETRY_DELAY: 5000, // 5 seconds between retries
    MAX_RETRIES: 3
  },

  // Daily Limits
  LIMITS: {
    MAX_DAILY_EXP: 250,
    TARGET_DAILY_POINTS: 2000, // Target points per day
    MAX_CONTENT_PER_CATEGORY: 50 // Safety limit
  },

  // Logging Configuration
  LOGGING: {
    LEVEL: 'info', // debug, info, warn, error
    SAVE_TO_FILE: true,
    LOG_FILE: 'bot.log'
  }
};
