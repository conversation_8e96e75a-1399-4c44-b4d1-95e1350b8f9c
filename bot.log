[2025-07-13 19:11:34] [INFO] 🎮 Starting bot in full mode
[2025-07-13 19:11:34] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:11:34] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:11:34] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:11:34] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:11:34] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:11:34] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:11:34] [ERROR] ❌ Authentication initialization failed "Login failed after token generation"
[2025-07-13 19:11:34] [ERROR] ❌ Bot initialization failed "Login failed after token generation"
[2025-07-13 19:11:34] [ERROR] ❌ Bot execution failed "Login failed after token generation"
[2025-07-13 19:11:34] [INFO] 📊 Session Summary:
[2025-07-13 19:11:34] [INFO]    Duration: a few seconds
[2025-07-13 19:11:34] [INFO]    Total Points: 0
[2025-07-13 19:11:34] [INFO]    Content Processed: 0 (0A, 0V, 0C)
[2025-07-13 19:11:34] [INFO]    Points/Minute: 0
[2025-07-13 19:11:34] [INFO]    Errors: 0
[2025-07-13 19:11:34] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:11:34] [ERROR] 💥 Fatal error "Login failed after token generation"
[2025-07-13 19:19:46] [INFO] 🧪 Testing Authentication System...
[2025-07-13 19:19:46] [INFO] 📝 Test 1: Token Generation
[2025-07-13 19:19:46] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:46] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:46] [DEBUG] Token details {"token":"FS6mAen6xx...","expiry":"2025-07-14T12:19:23.000Z"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated: FS6mAen6xxttC9PztcA3...
[2025-07-13 19:19:46] [INFO]    Expiry: Mon Jul 14 2025 19:19:23 GMT+0700 (Western Indonesia Time)
[2025-07-13 19:19:46] [INFO] 📝 Test 2: Login Status Check
[2025-07-13 19:19:46] [DEBUG] 🔍 Checking login status with token: FS6mAen6xx...
[2025-07-13 19:19:46] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.44 MB","elapse_time":"0.03","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:46] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:46] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.44 MB\",\n  \"elapse_time\": \"0.03\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:46] [INFO]    Login Status: false
[2025-07-13 19:19:46] [INFO] 📝 Test 3: User Profile
[2025-07-13 19:19:46] [INFO] 👤 Fetching user profile...
[2025-07-13 19:19:46] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:19:46] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"2.06 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:19:46] [ERROR] ❌ Profile failed: Request failed with status code 403
[2025-07-13 19:19:46] [INFO] 📝 Test 4: Loyalty Info
[2025-07-13 19:19:46] [ERROR] ❌ Failed to get loyalty info "Request failed with status code 403"
[2025-07-13 19:19:46] [ERROR] ❌ Loyalty failed: Request failed with status code 403
[2025-07-13 19:19:46] [INFO] 📝 Test 5: Full Initialize
[2025-07-13 19:19:46] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:19:46] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:46] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:46] [DEBUG] Token details {"token":"h9dNGIrYFe...","expiry":"2025-07-14T12:19:47.000Z"}
[2025-07-13 19:19:46] [DEBUG] ⏳ Waiting for token to become active...
[2025-07-13 19:19:48] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:48] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"0.00 Byte","elapse_time":"0.13","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:48] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:48] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"0.00 Byte\",\n  \"elapse_time\": \"0.13\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:48] [WARN] ⚠️ Login status inactive, retrying... (1/3)
[2025-07-13 19:19:51] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:51] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:51] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:51] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:51] [WARN] ⚠️ Login status inactive, retrying... (2/3)
[2025-07-13 19:19:54] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:54] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.69 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:54] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:54] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.69 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:54] [WARN] 🔄 Attempting token regeneration...
[2025-07-13 19:19:54] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:54] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:55] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:55] [DEBUG] Token details {"token":"IUeUoytXYa...","expiry":"2025-07-14T12:19:55.000Z"}
[2025-07-13 19:19:57] [DEBUG] 🔍 Checking login status with token: IUeUoytXYa...
[2025-07-13 19:19:57] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.34 MB","elapse_time":"0.04","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:57] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:57] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.34 MB\",\n  \"elapse_time\": \"0.04\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:57] [WARN] 🔧 Trying alternative authentication approach...
[2025-07-13 19:19:57] [INFO] 👤 Fetching user profile...
[2025-07-13 19:19:57] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:19:57] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"1.11 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:19:57] [ERROR] ❌ Token validation failed "Request failed with status code 403"
[2025-07-13 19:19:57] [ERROR] ❌ Authentication initialization failed "Authentication failed: Token invalid and login status inactive"
[2025-07-13 19:19:57] [ERROR] ❌ Full initialization failed: Authentication failed: Token invalid and login status inactive
[2025-07-13 19:19:57] [SUCCESS] 🎉 Authentication testing completed
[2025-07-13 19:28:55] [INFO] 🎮 Starting bot in quick mode
[2025-07-13 19:28:55] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:28:55] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:28:55] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:28:55] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:28:55] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r"}
[2025-07-13 19:28:55] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:28:55] [DEBUG] Token details {"token":"m3v6Xli6C5...","expiry":"2025-07-14T12:28:42.000Z"}
[2025-07-13 19:28:55] [DEBUG] ⏳ Waiting for token to become active...
[2025-07-13 19:28:57] [DEBUG] 🔍 Checking login status with token: m3v6Xli6C5...
[2025-07-13 19:28:57] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.21 MB","elapse_time":"0.02","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:28:57] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:28:57] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.21 MB\",\n  \"elapse_time\": \"0.02\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:28:57] [WARN] ⚠️ Login status inactive, retrying... (1/3)
[2025-07-13 19:29:00] [DEBUG] 🔍 Checking login status with token: m3v6Xli6C5...
[2025-07-13 19:29:00] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.03","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:29:00] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:29:00] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.03\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:29:00] [WARN] ⚠️ Login status inactive, retrying... (2/3)
[2025-07-13 19:29:03] [DEBUG] 🔍 Checking login status with token: m3v6Xli6C5...
[2025-07-13 19:29:03] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:29:03] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:29:03] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:29:03] [WARN] 🔄 Attempting token regeneration...
[2025-07-13 19:29:03] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:29:03] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r"}
[2025-07-13 19:29:03] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:29:03] [DEBUG] Token details {"token":"Dlsmfsl1DK...","expiry":"2025-07-14T12:29:04.000Z"}
[2025-07-13 19:29:05] [DEBUG] 🔍 Checking login status with token: Dlsmfsl1DK...
[2025-07-13 19:29:05] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:29:05] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:29:05] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:29:05] [WARN] 🔧 Trying alternative authentication approach...
[2025-07-13 19:29:05] [DEBUG] 🔗 Requesting AllAccess URL...
[2025-07-13 19:29:06] [DEBUG] ✅ AllAccess URL received
[2025-07-13 19:29:06] [DEBUG]    URL: https://allaccess.id/login?auth_profile=0f34011d7c0fd3511e3e23b73ea2a90ceb1882fa121071ff653a34f5de88...
[2025-07-13 19:29:06] [DEBUG]    Platform: amild x all
[2025-07-13 19:29:06] [DEBUG]    Visit AllAccess: true
[2025-07-13 19:29:06] [INFO] ✅ AllAccess URL request successful, token appears valid
[2025-07-13 19:29:06] [INFO] 👤 Fetching user profile...
[2025-07-13 19:29:06] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:29:06] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:29:06] [ERROR] ❌ Authentication initialization failed "Request failed with status code 403"
[2025-07-13 19:29:06] [ERROR] ❌ Bot initialization failed "Request failed with status code 403"
[2025-07-13 19:29:06] [ERROR] ❌ Bot execution failed "Request failed with status code 403"
[2025-07-13 19:29:06] [INFO] 📊 Session Summary:
[2025-07-13 19:29:06] [INFO]    Duration: a few seconds
[2025-07-13 19:29:06] [INFO]    Total Points: 0
[2025-07-13 19:29:06] [INFO]    Content Processed: 0 (0A, 0V, 0C)
[2025-07-13 19:29:06] [INFO]    Points/Minute: 0
[2025-07-13 19:29:06] [INFO]    Errors: 0
[2025-07-13 19:29:06] [DEBUG] 💾 Daily stats saved
[2025-07-13 19:29:06] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:29:06] [ERROR] 💥 Fatal error "Request failed with status code 403"
[2025-07-13 19:31:53] [INFO] 🎮 Starting bot in quick mode
[2025-07-13 19:31:53] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:31:53] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:31:53] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:31:53] [INFO] 🔑 Using working token from config...
[2025-07-13 19:31:53] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:31:53] [INFO] 👤 Fetching user profile...
[2025-07-13 19:31:54] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:31:54] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:31:54] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:31:54] [INFO] 📊 Loyalty Info - EXP: 4200 | Daily: 300/250
[2025-07-13 19:31:54] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:31:54] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:31:54] [INFO] 🚀 Session started
[2025-07-13 19:31:54] [INFO] 📊 Checking current progress...
[2025-07-13 19:31:55] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:31:55] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:31:55] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:31:55] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:36:41] [INFO] 🎮 Starting bot in unlimited mode
[2025-07-13 19:36:41] [INFO] ⚡ Unlimited mode: Processing content without daily EXP limit
[2025-07-13 19:36:41] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:36:41] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:36:41] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:36:41] [INFO] 🔑 Using working token from config...
[2025-07-13 19:36:41] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:36:41] [INFO] 👤 Fetching user profile...
[2025-07-13 19:36:42] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:36:42] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:36:42] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:36:42] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:36:42] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:36:42] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:36:42] [INFO] 🚀 Session started
[2025-07-13 19:36:42] [INFO] 📊 Checking current progress...
[2025-07-13 19:36:42] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:36:42] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:36:42] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:36:42] [INFO] 💡 Use "npm start unlimited" to process unlimited content only
[2025-07-13 19:36:42] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:36:59] [INFO] 🎮 Starting bot in unlimited mode
[2025-07-13 19:36:59] [INFO] ⚡ Unlimited mode: Processing content without daily EXP limit
[2025-07-13 19:36:59] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:36:59] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:36:59] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:36:59] [INFO] 🔑 Using working token from config...
[2025-07-13 19:36:59] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:36:59] [INFO] 👤 Fetching user profile...
[2025-07-13 19:37:00] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:37:00] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:37:00] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:37:00] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:37:00] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:37:00] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:37:00] [INFO] 🚀 Session started
[2025-07-13 19:37:00] [INFO] 📊 Checking current progress...
[2025-07-13 19:37:01] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:37:01] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:37:01] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:37:01] [INFO] 💡 Use "npm start unlimited" to process unlimited content only
[2025-07-13 19:37:01] [INFO] 🛑 Amild Auto Farming Bot Stopped
