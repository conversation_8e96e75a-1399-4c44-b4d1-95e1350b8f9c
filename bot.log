[2025-07-13 19:11:34] [INFO] 🎮 Starting bot in full mode
[2025-07-13 19:11:34] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:11:34] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:11:34] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:11:34] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:11:34] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:11:34] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:11:34] [ERROR] ❌ Authentication initialization failed "Login failed after token generation"
[2025-07-13 19:11:34] [ERROR] ❌ Bot initialization failed "Login failed after token generation"
[2025-07-13 19:11:34] [ERROR] ❌ Bot execution failed "Login failed after token generation"
[2025-07-13 19:11:34] [INFO] 📊 Session Summary:
[2025-07-13 19:11:34] [INFO]    Duration: a few seconds
[2025-07-13 19:11:34] [INFO]    Total Points: 0
[2025-07-13 19:11:34] [INFO]    Content Processed: 0 (0A, 0V, 0C)
[2025-07-13 19:11:34] [INFO]    Points/Minute: 0
[2025-07-13 19:11:34] [INFO]    Errors: 0
[2025-07-13 19:11:34] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:11:34] [ERROR] 💥 Fatal error "Login failed after token generation"
[2025-07-13 19:19:46] [INFO] 🧪 Testing Authentication System...
[2025-07-13 19:19:46] [INFO] 📝 Test 1: Token Generation
[2025-07-13 19:19:46] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:46] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:46] [DEBUG] Token details {"token":"FS6mAen6xx...","expiry":"2025-07-14T12:19:23.000Z"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated: FS6mAen6xxttC9PztcA3...
[2025-07-13 19:19:46] [INFO]    Expiry: Mon Jul 14 2025 19:19:23 GMT+0700 (Western Indonesia Time)
[2025-07-13 19:19:46] [INFO] 📝 Test 2: Login Status Check
[2025-07-13 19:19:46] [DEBUG] 🔍 Checking login status with token: FS6mAen6xx...
[2025-07-13 19:19:46] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.44 MB","elapse_time":"0.03","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:46] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:46] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.44 MB\",\n  \"elapse_time\": \"0.03\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:46] [INFO]    Login Status: false
[2025-07-13 19:19:46] [INFO] 📝 Test 3: User Profile
[2025-07-13 19:19:46] [INFO] 👤 Fetching user profile...
[2025-07-13 19:19:46] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:19:46] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"2.06 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:19:46] [ERROR] ❌ Profile failed: Request failed with status code 403
[2025-07-13 19:19:46] [INFO] 📝 Test 4: Loyalty Info
[2025-07-13 19:19:46] [ERROR] ❌ Failed to get loyalty info "Request failed with status code 403"
[2025-07-13 19:19:46] [ERROR] ❌ Loyalty failed: Request failed with status code 403
[2025-07-13 19:19:46] [INFO] 📝 Test 5: Full Initialize
[2025-07-13 19:19:46] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:19:46] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:46] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:46] [DEBUG] Token details {"token":"h9dNGIrYFe...","expiry":"2025-07-14T12:19:47.000Z"}
[2025-07-13 19:19:46] [DEBUG] ⏳ Waiting for token to become active...
[2025-07-13 19:19:48] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:48] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"0.00 Byte","elapse_time":"0.13","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:48] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:48] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"0.00 Byte\",\n  \"elapse_time\": \"0.13\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:48] [WARN] ⚠️ Login status inactive, retrying... (1/3)
[2025-07-13 19:19:51] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:51] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:51] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:51] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:51] [WARN] ⚠️ Login status inactive, retrying... (2/3)
[2025-07-13 19:19:54] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:54] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.69 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:54] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:54] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.69 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:54] [WARN] 🔄 Attempting token regeneration...
[2025-07-13 19:19:54] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:54] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:55] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:55] [DEBUG] Token details {"token":"IUeUoytXYa...","expiry":"2025-07-14T12:19:55.000Z"}
[2025-07-13 19:19:57] [DEBUG] 🔍 Checking login status with token: IUeUoytXYa...
[2025-07-13 19:19:57] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.34 MB","elapse_time":"0.04","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:57] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:57] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.34 MB\",\n  \"elapse_time\": \"0.04\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:57] [WARN] 🔧 Trying alternative authentication approach...
[2025-07-13 19:19:57] [INFO] 👤 Fetching user profile...
[2025-07-13 19:19:57] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:19:57] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"1.11 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:19:57] [ERROR] ❌ Token validation failed "Request failed with status code 403"
[2025-07-13 19:19:57] [ERROR] ❌ Authentication initialization failed "Authentication failed: Token invalid and login status inactive"
[2025-07-13 19:19:57] [ERROR] ❌ Full initialization failed: Authentication failed: Token invalid and login status inactive
[2025-07-13 19:19:57] [SUCCESS] 🎉 Authentication testing completed
