[2025-07-13 19:11:34] [INFO] 🎮 Starting bot in full mode
[2025-07-13 19:11:34] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:11:34] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:11:34] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:11:34] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:11:34] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:11:34] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:11:34] [ERROR] ❌ Authentication initialization failed "Login failed after token generation"
[2025-07-13 19:11:34] [ERROR] ❌ Bot initialization failed "Login failed after token generation"
[2025-07-13 19:11:34] [ERROR] ❌ Bot execution failed "Login failed after token generation"
[2025-07-13 19:11:34] [INFO] 📊 Session Summary:
[2025-07-13 19:11:34] [INFO]    Duration: a few seconds
[2025-07-13 19:11:34] [INFO]    Total Points: 0
[2025-07-13 19:11:34] [INFO]    Content Processed: 0 (0A, 0V, 0C)
[2025-07-13 19:11:34] [INFO]    Points/Minute: 0
[2025-07-13 19:11:34] [INFO]    Errors: 0
[2025-07-13 19:11:34] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:11:34] [ERROR] 💥 Fatal error "Login failed after token generation"
[2025-07-13 19:19:46] [INFO] 🧪 Testing Authentication System...
[2025-07-13 19:19:46] [INFO] 📝 Test 1: Token Generation
[2025-07-13 19:19:46] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:46] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:46] [DEBUG] Token details {"token":"FS6mAen6xx...","expiry":"2025-07-14T12:19:23.000Z"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated: FS6mAen6xxttC9PztcA3...
[2025-07-13 19:19:46] [INFO]    Expiry: Mon Jul 14 2025 19:19:23 GMT+0700 (Western Indonesia Time)
[2025-07-13 19:19:46] [INFO] 📝 Test 2: Login Status Check
[2025-07-13 19:19:46] [DEBUG] 🔍 Checking login status with token: FS6mAen6xx...
[2025-07-13 19:19:46] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.44 MB","elapse_time":"0.03","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:46] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:46] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.44 MB\",\n  \"elapse_time\": \"0.03\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:46] [INFO]    Login Status: false
[2025-07-13 19:19:46] [INFO] 📝 Test 3: User Profile
[2025-07-13 19:19:46] [INFO] 👤 Fetching user profile...
[2025-07-13 19:19:46] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:19:46] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"2.06 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:19:46] [ERROR] ❌ Profile failed: Request failed with status code 403
[2025-07-13 19:19:46] [INFO] 📝 Test 4: Loyalty Info
[2025-07-13 19:19:46] [ERROR] ❌ Failed to get loyalty info "Request failed with status code 403"
[2025-07-13 19:19:46] [ERROR] ❌ Loyalty failed: Request failed with status code 403
[2025-07-13 19:19:46] [INFO] 📝 Test 5: Full Initialize
[2025-07-13 19:19:46] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:19:46] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:46] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:46] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:46] [DEBUG] Token details {"token":"h9dNGIrYFe...","expiry":"2025-07-14T12:19:47.000Z"}
[2025-07-13 19:19:46] [DEBUG] ⏳ Waiting for token to become active...
[2025-07-13 19:19:48] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:48] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"0.00 Byte","elapse_time":"0.13","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:48] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:48] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"0.00 Byte\",\n  \"elapse_time\": \"0.13\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:48] [WARN] ⚠️ Login status inactive, retrying... (1/3)
[2025-07-13 19:19:51] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:51] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:51] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:51] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:51] [WARN] ⚠️ Login status inactive, retrying... (2/3)
[2025-07-13 19:19:54] [DEBUG] 🔍 Checking login status with token: h9dNGIrYFe...
[2025-07-13 19:19:54] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.69 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:54] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:54] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.69 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:54] [WARN] 🔄 Attempting token regeneration...
[2025-07-13 19:19:54] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:19:54] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL"}
[2025-07-13 19:19:55] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:19:55] [DEBUG] Token details {"token":"IUeUoytXYa...","expiry":"2025-07-14T12:19:55.000Z"}
[2025-07-13 19:19:57] [DEBUG] 🔍 Checking login status with token: IUeUoytXYa...
[2025-07-13 19:19:57] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.34 MB","elapse_time":"0.04","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:19:57] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:19:57] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.34 MB\",\n  \"elapse_time\": \"0.04\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:19:57] [WARN] 🔧 Trying alternative authentication approach...
[2025-07-13 19:19:57] [INFO] 👤 Fetching user profile...
[2025-07-13 19:19:57] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:19:57] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"1.11 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:19:57] [ERROR] ❌ Token validation failed "Request failed with status code 403"
[2025-07-13 19:19:57] [ERROR] ❌ Authentication initialization failed "Authentication failed: Token invalid and login status inactive"
[2025-07-13 19:19:57] [ERROR] ❌ Full initialization failed: Authentication failed: Token invalid and login status inactive
[2025-07-13 19:19:57] [SUCCESS] 🎉 Authentication testing completed
[2025-07-13 19:28:55] [INFO] 🎮 Starting bot in quick mode
[2025-07-13 19:28:55] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:28:55] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:28:55] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:28:55] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:28:55] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r"}
[2025-07-13 19:28:55] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:28:55] [DEBUG] Token details {"token":"m3v6Xli6C5...","expiry":"2025-07-14T12:28:42.000Z"}
[2025-07-13 19:28:55] [DEBUG] ⏳ Waiting for token to become active...
[2025-07-13 19:28:57] [DEBUG] 🔍 Checking login status with token: m3v6Xli6C5...
[2025-07-13 19:28:57] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"2.21 MB","elapse_time":"0.02","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:28:57] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:28:57] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"2.21 MB\",\n  \"elapse_time\": \"0.02\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:28:57] [WARN] ⚠️ Login status inactive, retrying... (1/3)
[2025-07-13 19:29:00] [DEBUG] 🔍 Checking login status with token: m3v6Xli6C5...
[2025-07-13 19:29:00] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.03","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:29:00] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:29:00] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.03\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:29:00] [WARN] ⚠️ Login status inactive, retrying... (2/3)
[2025-07-13 19:29:03] [DEBUG] 🔍 Checking login status with token: m3v6Xli6C5...
[2025-07-13 19:29:03] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:29:03] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:29:03] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:29:03] [WARN] 🔄 Attempting token regeneration...
[2025-07-13 19:29:03] [INFO] 🔑 Generating authentication token...
[2025-07-13 19:29:03] [DEBUG] Token request data: {"name":"amild","secret_key":"***","device_type":"website","app_key":"629DE67FBE4A67B65541","device_id":"meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r"}
[2025-07-13 19:29:03] [SUCCESS] ✅ Token generated successfully
[2025-07-13 19:29:03] [DEBUG] Token details {"token":"Dlsmfsl1DK...","expiry":"2025-07-14T12:29:04.000Z"}
[2025-07-13 19:29:05] [DEBUG] 🔍 Checking login status with token: Dlsmfsl1DK...
[2025-07-13 19:29:05] [DEBUG] Login status response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":200,"error":{},"data":{"name":"login_status","data":{"status":"Success","login_status":false}}}
[2025-07-13 19:29:05] [WARN] ⚠️ Login status: Inactive
[2025-07-13 19:29:05] [DEBUG] Full response data: "{\n  \"api_version\": \"1.9.0\",\n  \"memory_usage\": \"1.10 MB\",\n  \"elapse_time\": \"0.01\",\n  \"lang\": \"en\",\n  \"code\": 200,\n  \"error\": {},\n  \"data\": {\n    \"name\": \"login_status\",\n    \"data\": {\n      \"status\": \"Success\",\n      \"login_status\": false\n    }\n  }\n}"
[2025-07-13 19:29:05] [WARN] 🔧 Trying alternative authentication approach...
[2025-07-13 19:29:05] [DEBUG] 🔗 Requesting AllAccess URL...
[2025-07-13 19:29:06] [DEBUG] ✅ AllAccess URL received
[2025-07-13 19:29:06] [DEBUG]    URL: https://allaccess.id/login?auth_profile=0f34011d7c0fd3511e3e23b73ea2a90ceb1882fa121071ff653a34f5de88...
[2025-07-13 19:29:06] [DEBUG]    Platform: amild x all
[2025-07-13 19:29:06] [DEBUG]    Visit AllAccess: true
[2025-07-13 19:29:06] [INFO] ✅ AllAccess URL request successful, token appears valid
[2025-07-13 19:29:06] [INFO] 👤 Fetching user profile...
[2025-07-13 19:29:06] [ERROR] ❌ Failed to get user profile "Request failed with status code 403"
[2025-07-13 19:29:06] [DEBUG] Profile error response: {"api_version":"1.9.0","memory_usage":"1.10 MB","elapse_time":"0.01","lang":"en","code":403,"error":{"message":"Please login first!","errors":[{"code":403,"message":"Please login first!"}]},"data":{}}
[2025-07-13 19:29:06] [ERROR] ❌ Authentication initialization failed "Request failed with status code 403"
[2025-07-13 19:29:06] [ERROR] ❌ Bot initialization failed "Request failed with status code 403"
[2025-07-13 19:29:06] [ERROR] ❌ Bot execution failed "Request failed with status code 403"
[2025-07-13 19:29:06] [INFO] 📊 Session Summary:
[2025-07-13 19:29:06] [INFO]    Duration: a few seconds
[2025-07-13 19:29:06] [INFO]    Total Points: 0
[2025-07-13 19:29:06] [INFO]    Content Processed: 0 (0A, 0V, 0C)
[2025-07-13 19:29:06] [INFO]    Points/Minute: 0
[2025-07-13 19:29:06] [INFO]    Errors: 0
[2025-07-13 19:29:06] [DEBUG] 💾 Daily stats saved
[2025-07-13 19:29:06] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:29:06] [ERROR] 💥 Fatal error "Request failed with status code 403"
[2025-07-13 19:31:53] [INFO] 🎮 Starting bot in quick mode
[2025-07-13 19:31:53] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:31:53] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:31:53] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:31:53] [INFO] 🔑 Using working token from config...
[2025-07-13 19:31:53] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:31:53] [INFO] 👤 Fetching user profile...
[2025-07-13 19:31:54] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:31:54] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:31:54] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:31:54] [INFO] 📊 Loyalty Info - EXP: 4200 | Daily: 300/250
[2025-07-13 19:31:54] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:31:54] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:31:54] [INFO] 🚀 Session started
[2025-07-13 19:31:54] [INFO] 📊 Checking current progress...
[2025-07-13 19:31:55] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:31:55] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:31:55] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:31:55] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:36:41] [INFO] 🎮 Starting bot in unlimited mode
[2025-07-13 19:36:41] [INFO] ⚡ Unlimited mode: Processing content without daily EXP limit
[2025-07-13 19:36:41] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:36:41] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:36:41] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:36:41] [INFO] 🔑 Using working token from config...
[2025-07-13 19:36:41] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:36:41] [INFO] 👤 Fetching user profile...
[2025-07-13 19:36:42] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:36:42] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:36:42] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:36:42] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:36:42] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:36:42] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:36:42] [INFO] 🚀 Session started
[2025-07-13 19:36:42] [INFO] 📊 Checking current progress...
[2025-07-13 19:36:42] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:36:42] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:36:42] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:36:42] [INFO] 💡 Use "npm start unlimited" to process unlimited content only
[2025-07-13 19:36:42] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:36:59] [INFO] 🎮 Starting bot in unlimited mode
[2025-07-13 19:36:59] [INFO] ⚡ Unlimited mode: Processing content without daily EXP limit
[2025-07-13 19:36:59] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:36:59] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:36:59] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:36:59] [INFO] 🔑 Using working token from config...
[2025-07-13 19:36:59] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:36:59] [INFO] 👤 Fetching user profile...
[2025-07-13 19:37:00] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:37:00] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:37:00] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:37:00] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:37:00] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:37:00] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:37:00] [INFO] 🚀 Session started
[2025-07-13 19:37:00] [INFO] 📊 Checking current progress...
[2025-07-13 19:37:01] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:37:01] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:37:01] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:37:01] [INFO] 💡 Use "npm start unlimited" to process unlimited content only
[2025-07-13 19:37:01] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:40:24] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:40:24] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:40:24] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:40:24] [INFO] 🔑 Using working token from config...
[2025-07-13 19:40:24] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:40:24] [INFO] 👤 Fetching user profile...
[2025-07-13 19:40:25] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:40:25] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:40:25] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:40:26] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:40:26] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:40:26] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:40:26] [INFO] 🚀 Session started
[2025-07-13 19:40:26] [INFO] 📊 Checking current progress...
[2025-07-13 19:40:26] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:40:26] [INFO] ⚡ Daily EXP limit bypassed for unlimited content
[2025-07-13 19:40:26] [SUCCESS] ✅ Bot initialized successfully
[2025-07-13 19:40:26] [INFO] 📊 Checking current progress...
[2025-07-13 19:40:26] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:40:26] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:40:26] [INFO] 🔍 Starting content discovery...
[2025-07-13 19:40:26] [INFO] 🔍 Starting comprehensive content discovery...
[2025-07-13 19:40:26] [INFO] 🎯 Discovering campaign content: gac-2024
[2025-07-13 19:40:27] [SUCCESS] ✅ Campaign gac-2024: 2 items discovered
[2025-07-13 19:40:27] [INFO] 🎯 Discovering campaign content: inspiraksi2025
[2025-07-13 19:40:28] [SUCCESS] ✅ Campaign inspiraksi2025: 4 items discovered
[2025-07-13 19:40:28] [INFO] 🎯 Discovering campaign content: go-ahead-music
[2025-07-13 19:40:29] [SUCCESS] ✅ Campaign go-ahead-music: 8 items discovered
[2025-07-13 19:40:29] [INFO] 📚 Discovering content in category: music
[2025-07-13 19:40:34] [SUCCESS] ✅ Category music: 31 items discovered
[2025-07-13 19:40:34] [INFO] 📚 Discovering content in category: lifestyle
[2025-07-13 19:40:35] [SUCCESS] ✅ Category lifestyle: 4 items discovered
[2025-07-13 19:40:35] [INFO] 📚 Discovering content in category: daily-life
[2025-07-13 19:40:38] [SUCCESS] ✅ Category daily-life: 14 items discovered
[2025-07-13 19:40:38] [INFO] 📚 Discovering content in category: travel
[2025-07-13 19:40:38] [SUCCESS] ✅ Category travel: 9 items discovered
[2025-07-13 19:40:38] [INFO] 📚 Discovering content in category: art
[2025-07-13 19:40:39] [SUCCESS] ✅ Category art: 4 items discovered
[2025-07-13 19:40:39] [INFO] 📚 Discovering content in category: sustainability
[2025-07-13 19:40:40] [SUCCESS] ✅ Category sustainability: 4 items discovered
[2025-07-13 19:40:40] [SUCCESS] ✅ Content discovery complete: 66 unique items found
[2025-07-13 19:40:40] [INFO] 📊 Content Summary:
[2025-07-13 19:40:40] [INFO]    Total: 66 items
[2025-07-13 19:40:40] [INFO]    Types: {"video":17,"article":32,"carousel":17}
[2025-07-13 19:40:40] [INFO]    Campaigns: {"gac-2024":2,"inspiraksi2025":4,"go-ahead-music":8,"why-not":11,"gac2024":11,"avolution-twilight-breeze":3,"uncompromising-a":5,"edisi-35th":17,"a-splash-gala":2,"inspiraksi":3}
[2025-07-13 19:40:40] [INFO]    Points: {"50":53,"100":11,"150":2}
[2025-07-13 19:40:40] [INFO]    Unlimited: 6 items
[2025-07-13 19:40:40] [SUCCESS] ✅ Content discovery complete: 66 items found
[2025-07-13 19:40:40] [INFO] 📚 Processing 1 articles...
[2025-07-13 19:40:40] [INFO] 📖 Processing article: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:40:40] [INFO]    Campaign: inspiraksi2025 | Points: 100 | Unlimited: true
[2025-07-13 19:40:44] [INFO] 📖 Simulating article reading: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:40:55] [SUCCESS] ✅ Article reading completed: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:40:55] [SUCCESS] 💰 +100 points from Article: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:40:55] [INFO] 📄 Article: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu! - [32mSUCCESS[39m
[2025-07-13 19:41:00] [INFO] 📊 Article Processing Summary:
[2025-07-13 19:41:01] [INFO]    Processed: 1
[2025-07-13 19:41:01] [INFO]    Successful: 1
[2025-07-13 19:41:01] [INFO]    Failed: 0
[2025-07-13 19:41:01] [INFO]    Total Points: 100
[2025-07-13 19:41:18] [INFO] 🎮 Starting bot in unlimited mode
[2025-07-13 19:41:18] [INFO] ⚡ Unlimited mode: Processing content without daily EXP limit
[2025-07-13 19:41:18] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:41:18] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:41:18] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:41:18] [INFO] 🔑 Using working token from config...
[2025-07-13 19:41:18] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:41:18] [INFO] 👤 Fetching user profile...
[2025-07-13 19:41:20] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:41:20] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:41:20] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:41:20] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:41:20] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:41:20] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:41:20] [INFO] 🚀 Session started
[2025-07-13 19:41:20] [INFO] 📊 Checking current progress...
[2025-07-13 19:41:20] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:41:20] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:41:20] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:41:20] [INFO] 💡 Use "npm start unlimited" to process unlimited content only
[2025-07-13 19:41:20] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:43:24] [INFO] 🎮 Starting bot in unlimited mode
[2025-07-13 19:43:24] [INFO] ⚡ Unlimited mode: Processing content without daily EXP limit
[2025-07-13 19:43:24] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:43:24] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:43:24] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:43:24] [INFO] 🔑 Using working token from config...
[2025-07-13 19:43:24] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:43:24] [INFO] 👤 Fetching user profile...
[2025-07-13 19:43:26] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:43:26] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:43:26] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:43:26] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:43:26] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:43:26] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:43:26] [INFO] 🚀 Session started
[2025-07-13 19:43:26] [INFO] 📊 Checking current progress...
[2025-07-13 19:43:26] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:43:26] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:43:26] [WARN] ⚠️ Daily limit already reached, exiting
[2025-07-13 19:43:26] [INFO] 💡 Use "npm start unlimited" to process unlimited content only
[2025-07-13 19:43:26] [INFO] 🛑 Amild Auto Farming Bot Stopped
[2025-07-13 19:44:23] [INFO] 🎮 Starting bot in unlimited mode
[2025-07-13 19:44:23] [INFO] ⚡ Unlimited mode: Processing content without daily EXP limit
[2025-07-13 19:44:23] [INFO] 🤖 Amild Auto Farming Bot Started
[2025-07-13 19:44:23] [INFO] 🔧 Initializing Amild Bot...
[2025-07-13 19:44:23] [INFO] 🚀 Initializing authentication...
[2025-07-13 19:44:23] [INFO] 🔑 Using working token from config...
[2025-07-13 19:44:23] [SUCCESS] ✅ Working token is still active
[2025-07-13 19:44:23] [INFO] 👤 Fetching user profile...
[2025-07-13 19:44:24] [SUCCESS] ✅ User profile loaded
[2025-07-13 19:44:24] [INFO] User: Doni Setiawan (<EMAIL>)
[2025-07-13 19:44:24] [INFO] Tier: 2 | Status: stay
[2025-07-13 19:44:24] [INFO] 📊 Loyalty Info - EXP: 4300 | Daily: 300/250
[2025-07-13 19:44:24] [INFO] 🏆 Current Tier: Silver → Next: Gold
[2025-07-13 19:44:24] [SUCCESS] ✅ Authentication initialized successfully with working token
[2025-07-13 19:44:24] [INFO] 🚀 Session started
[2025-07-13 19:44:24] [INFO] 📊 Checking current progress...
[2025-07-13 19:44:25] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:44:25] [INFO] ⚡ Daily EXP limit bypassed for unlimited content
[2025-07-13 19:44:25] [SUCCESS] ✅ Bot initialized successfully
[2025-07-13 19:44:25] [INFO] ⚡ Starting unlimited content processing...
[2025-07-13 19:44:25] [INFO] 🔍 Starting content discovery...
[2025-07-13 19:44:25] [INFO] 🔍 Starting comprehensive content discovery...
[2025-07-13 19:44:25] [INFO] 🎯 Discovering campaign content: gac-2024
[2025-07-13 19:44:25] [SUCCESS] ✅ Campaign gac-2024: 2 items discovered
[2025-07-13 19:44:25] [INFO] 🎯 Discovering campaign content: inspiraksi2025
[2025-07-13 19:44:27] [SUCCESS] ✅ Campaign inspiraksi2025: 4 items discovered
[2025-07-13 19:44:27] [INFO] 🎯 Discovering campaign content: go-ahead-music
[2025-07-13 19:44:29] [SUCCESS] ✅ Campaign go-ahead-music: 8 items discovered
[2025-07-13 19:44:29] [INFO] 📚 Discovering content in category: music
[2025-07-13 19:44:34] [SUCCESS] ✅ Category music: 31 items discovered
[2025-07-13 19:44:34] [INFO] 📚 Discovering content in category: lifestyle
[2025-07-13 19:44:35] [SUCCESS] ✅ Category lifestyle: 4 items discovered
[2025-07-13 19:44:35] [INFO] 📚 Discovering content in category: daily-life
[2025-07-13 19:44:37] [SUCCESS] ✅ Category daily-life: 14 items discovered
[2025-07-13 19:44:37] [INFO] 📚 Discovering content in category: travel
[2025-07-13 19:44:38] [SUCCESS] ✅ Category travel: 9 items discovered
[2025-07-13 19:44:38] [INFO] 📚 Discovering content in category: art
[2025-07-13 19:44:39] [SUCCESS] ✅ Category art: 4 items discovered
[2025-07-13 19:44:39] [INFO] 📚 Discovering content in category: sustainability
[2025-07-13 19:44:40] [SUCCESS] ✅ Category sustainability: 4 items discovered
[2025-07-13 19:44:40] [SUCCESS] ✅ Content discovery complete: 66 unique items found
[2025-07-13 19:44:40] [INFO] 📊 Content Summary:
[2025-07-13 19:44:40] [INFO]    Total: 66 items
[2025-07-13 19:44:40] [INFO]    Types: {"video":17,"article":32,"carousel":17}
[2025-07-13 19:44:40] [INFO]    Campaigns: {"gac-2024":2,"inspiraksi2025":4,"go-ahead-music":8,"why-not":11,"gac2024":11,"avolution-twilight-breeze":3,"uncompromising-a":5,"edisi-35th":17,"a-splash-gala":2,"inspiraksi":3}
[2025-07-13 19:44:40] [INFO]    Points: {"50":53,"100":11,"150":2}
[2025-07-13 19:44:40] [INFO]    Unlimited: 6 items
[2025-07-13 19:44:40] [SUCCESS] ✅ Content discovery complete: 66 items found
[2025-07-13 19:44:40] [INFO] 🎯 Found 6 unlimited items
[2025-07-13 19:44:40] [INFO] 🚀 Starting content processing...
[2025-07-13 19:44:40] [INFO] 🧠 Creating processing strategy...
[2025-07-13 19:44:40] [INFO]    Total Content: 6 items
[2025-07-13 19:44:40] [INFO]    Estimated Points: 450
[2025-07-13 19:44:40] [INFO]    Estimated Time: 1m 30s
[2025-07-13 19:44:40] [INFO]    High Value: 3 items (100+ points)
[2025-07-13 19:44:40] [INFO]    Unlimited: 6 items
[2025-07-13 19:44:40] [INFO] 📋 Strategy Created:
[2025-07-13 19:44:40] [INFO]    Phase 1: Unlimited High-Value (3 items, 300 points)
[2025-07-13 19:44:40] [INFO]    Phase 2: Medium-Value Content (3 items, 150 points)
[2025-07-13 19:44:40] [INFO]    Total Estimated: 450 points in 1m 30s
[2025-07-13 19:44:40] [INFO] ⏰ Optimizing strategy for 1h 0m 0s time limit
[2025-07-13 19:44:40] [INFO] ✅ Optimized strategy: 450 points in 1m 30s
[2025-07-13 19:44:40] [INFO] 📋 Processing Phase: Unlimited High-Value (3 items)
[2025-07-13 19:44:40] [INFO] 📚 Processing 3 articles...
[2025-07-13 19:44:40] [INFO] 📖 Processing article: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:44:40] [INFO]    Campaign: inspiraksi2025 | Points: 100 | Unlimited: true
[2025-07-13 19:44:43] [INFO] 📖 Simulating article reading: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:44:54] [SUCCESS] ✅ Article reading completed: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:44:54] [SUCCESS] 💰 +100 points from Article: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu!
[2025-07-13 19:44:54] [INFO] 📄 Article: Barang Lama Juga Bisa Bikin Perubahan Baru, Mulai Aja Dulu! - [32mSUCCESS[39m
[2025-07-13 19:44:59] [INFO] 📖 Processing article: Solois Baru Bisa Punya Komunitas yang Solid, Emang Bisa?
[2025-07-13 19:44:59] [INFO]    Campaign: go-ahead-music | Points: 100 | Unlimited: true
[2025-07-13 19:45:02] [INFO] 📖 Simulating article reading: Solois Baru Bisa Punya Komunitas yang Solid, Emang Bisa?
[2025-07-13 19:45:14] [SUCCESS] ✅ Article reading completed: Solois Baru Bisa Punya Komunitas yang Solid, Emang Bisa?
[2025-07-13 19:45:14] [SUCCESS] 💰 +100 points from Article: Solois Baru Bisa Punya Komunitas yang Solid, Emang Bisa?
[2025-07-13 19:45:14] [INFO] 📄 Article: Solois Baru Bisa Punya Komunitas yang Solid, Emang Bisa? - [32mSUCCESS[39m
[2025-07-13 19:45:21] [INFO] 📖 Processing article: Belajar Skill Baru di Waktu Kosong
[2025-07-13 19:45:21] [INFO]    Campaign: why-not | Points: 100 | Unlimited: true
[2025-07-13 19:45:25] [INFO] 📖 Simulating article reading: Belajar Skill Baru di Waktu Kosong
[2025-07-13 19:45:37] [SUCCESS] ✅ Article reading completed: Belajar Skill Baru di Waktu Kosong
[2025-07-13 19:45:37] [SUCCESS] 💰 +100 points from Article: Belajar Skill Baru di Waktu Kosong
[2025-07-13 19:45:37] [INFO] 📄 Article: Belajar Skill Baru di Waktu Kosong - [32mSUCCESS[39m
[2025-07-13 19:45:43] [INFO] 📊 Article Processing Summary:
[2025-07-13 19:45:43] [INFO]    Processed: 3
[2025-07-13 19:45:43] [INFO]    Successful: 3
[2025-07-13 19:45:43] [INFO]    Failed: 0
[2025-07-13 19:45:43] [INFO]    Total Points: 300
[2025-07-13 19:45:43] [INFO] 📊 Checking current progress...
[2025-07-13 19:45:43] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:45:43] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:45:43] [SUCCESS] ✅ Phase completed: Unlimited High-Value
[2025-07-13 19:45:43] [INFO] 📋 Processing Phase: Medium-Value Content (3 items)
[2025-07-13 19:45:43] [INFO] 📚 Processing 3 articles...
[2025-07-13 19:45:43] [INFO] 📖 Processing article: Mau Mulai Nge-band, Idealis atau Realistis?
[2025-07-13 19:45:43] [INFO]    Campaign: go-ahead-music | Points: 50 | Unlimited: true
[2025-07-13 19:45:46] [INFO] 📖 Simulating article reading: Mau Mulai Nge-band, Idealis atau Realistis?
[2025-07-13 19:45:58] [SUCCESS] ✅ Article reading completed: Mau Mulai Nge-band, Idealis atau Realistis?
[2025-07-13 19:45:58] [SUCCESS] 💰 +50 points from Article: Mau Mulai Nge-band, Idealis atau Realistis?
[2025-07-13 19:45:58] [INFO] 📄 Article: Mau Mulai Nge-band, Idealis atau Realistis? - [32mSUCCESS[39m
[2025-07-13 19:46:04] [INFO] 📖 Processing article: Bangun Personal Branding Lewat Musik yang Lo Suka!
[2025-07-13 19:46:04] [INFO]    Campaign: why-not | Points: 50 | Unlimited: true
[2025-07-13 19:46:07] [INFO] 📖 Simulating article reading: Bangun Personal Branding Lewat Musik yang Lo Suka!
[2025-07-13 19:46:19] [SUCCESS] ✅ Article reading completed: Bangun Personal Branding Lewat Musik yang Lo Suka!
[2025-07-13 19:46:19] [SUCCESS] 💰 +50 points from Article: Bangun Personal Branding Lewat Musik yang Lo Suka!
[2025-07-13 19:46:19] [INFO] 📄 Article: Bangun Personal Branding Lewat Musik yang Lo Suka! - [32mSUCCESS[39m
[2025-07-13 19:46:22] [INFO] 📖 Processing article: Selangkah Lebih Besar dengan Cari Inspirasi di Tempat Baru
[2025-07-13 19:46:22] [INFO]    Campaign: why-not | Points: 50 | Unlimited: true
[2025-07-13 19:46:26] [INFO] 📖 Simulating article reading: Selangkah Lebih Besar dengan Cari Inspirasi di Tempat Baru
[2025-07-13 19:46:38] [SUCCESS] ✅ Article reading completed: Selangkah Lebih Besar dengan Cari Inspirasi di Tempat Baru
[2025-07-13 19:46:38] [SUCCESS] 💰 +50 points from Article: Selangkah Lebih Besar dengan Cari Inspirasi di Tempat Baru
[2025-07-13 19:46:38] [INFO] 📄 Article: Selangkah Lebih Besar dengan Cari Inspirasi di Tempat Baru - [32mSUCCESS[39m
[2025-07-13 19:46:42] [INFO] 📊 Article Processing Summary:
[2025-07-13 19:46:42] [INFO]    Processed: 3
[2025-07-13 19:46:42] [INFO]    Successful: 3
[2025-07-13 19:46:42] [INFO]    Failed: 0
[2025-07-13 19:46:42] [INFO]    Total Points: 150
[2025-07-13 19:46:42] [INFO] 📊 Checking current progress...
[2025-07-13 19:46:42] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:46:42] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:46:42] [SUCCESS] ✅ Phase completed: Medium-Value Content
[2025-07-13 19:46:42] [INFO] 📊 Checking current progress...
[2025-07-13 19:46:42] [INFO] 📊 Progress: 0 points | Daily EXP: 300/250 | Tier: Silver
[2025-07-13 19:46:42] [WARN] ⚠️ Daily EXP limit reached!
[2025-07-13 19:46:42] [INFO] 📊 Session Summary:
[2025-07-13 19:46:42] [INFO]    Duration: 2 minutes
[2025-07-13 19:46:42] [INFO]    Total Points: 450
[2025-07-13 19:46:42] [INFO]    Content Processed: 6 (6A, 0V, 0C)
[2025-07-13 19:46:42] [INFO]    Points/Minute: 196
[2025-07-13 19:46:42] [INFO]    Errors: 0
[2025-07-13 19:46:42] [INFO] 📅 Daily Summary:
[2025-07-13 19:46:42] [INFO]    Date: 2025-07-13
[2025-07-13 19:46:42] [INFO]    Total Points: 450
[2025-07-13 19:46:42] [INFO]    Daily EXP: 300/250
[2025-07-13 19:46:42] [INFO]    Content Completed: 6 (6A, 0V, 0C)
[2025-07-13 19:46:42] [INFO]    Sessions Run: 7
[2025-07-13 19:46:42] [INFO]    Current Tier: Silver
[2025-07-13 19:46:42] [SUCCESS] 🎉 Bot execution completed successfully
[2025-07-13 19:46:42] [INFO] 🛑 Amild Auto Farming Bot Stopped
