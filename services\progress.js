/**
 * Progress Monitoring Service
 * Creates point tracking, tier monitoring, and daily progress reporting system
 */

const axios = require('axios');
const fs = require('fs-extra');
const moment = require('moment');
const config = require('../config');
const logger = require('../utils/logger');

class ProgressService {
  constructor(authService) {
    this.auth = authService;
    this.sessionStats = {
      startTime: new Date(),
      totalPoints: 0,
      articlesProcessed: 0,
      videosProcessed: 0,
      carouselsProcessed: 0,
      errors: 0
    };
    this.dailyStats = this.loadDailyStats();
  }

  /**
   * Load daily stats from file
   */
  loadDailyStats() {
    try {
      const today = moment().format('YYYY-MM-DD');
      const statsFile = `stats_${today}.json`;
      
      if (fs.existsSync(statsFile)) {
        return fs.readJsonSync(statsFile);
      } else {
        return {
          date: today,
          totalPoints: 0,
          dailyExp: 0,
          articlesCompleted: 0,
          videosCompleted: 0,
          carouselsCompleted: 0,
          sessionsRun: 0,
          startTier: null,
          currentTier: null,
          campaigns: {}
        };
      }
    } catch (error) {
      logger.warn('⚠️ Failed to load daily stats, starting fresh');
      return {
        date: moment().format('YYYY-MM-DD'),
        totalPoints: 0,
        dailyExp: 0,
        articlesCompleted: 0,
        videosCompleted: 0,
        carouselsCompleted: 0,
        sessionsRun: 0,
        startTier: null,
        currentTier: null,
        campaigns: {}
      };
    }
  }

  /**
   * Save daily stats to file
   */
  saveDailyStats() {
    try {
      const today = moment().format('YYYY-MM-DD');
      const statsFile = `stats_${today}.json`;
      
      this.dailyStats.date = today;
      fs.writeJsonSync(statsFile, this.dailyStats, { spaces: 2 });
      
      logger.debug('💾 Daily stats saved');
    } catch (error) {
      logger.error('❌ Failed to save daily stats', error.message);
    }
  }

  /**
   * Get current UB points
   */
  async getCurrentPoints() {
    try {
      const response = await axios.post(`${config.API.BASE_URL}/api/fs/feed/ub-point`, {
        content_type: ''
      }, {
        headers: this.auth.getAuthHeaders()
      });

      if (response.data.code === 200) {
        return response.data.data.point;
      } else {
        throw new Error('Invalid points response');
      }
    } catch (error) {
      logger.error('❌ Failed to get current points', error.message);
      return 0;
    }
  }

  /**
   * Get current loyalty information
   */
  async getCurrentLoyalty() {
    try {
      const response = await axios.post(`${config.API.BASE_URL}/api/fs/user/loyalty-teaser`, {}, {
        headers: this.auth.getAuthHeaders()
      });

      if (response.data.code === 200) {
        const data = response.data.data.data;
        return {
          firstName: data.fist_name,
          accumulatedExp: data.accumulated_exp,
          currentTier: data.current_tier,
          nextTier: data.next_tier,
          dailyExp: data.daily_exp,
          maxDailyExp: data.max_daily_exp,
          statusTier: data.status_tier
        };
      } else {
        throw new Error('Invalid loyalty response');
      }
    } catch (error) {
      logger.error('❌ Failed to get loyalty info', error.message);
      return null;
    }
  }

  /**
   * Update session stats
   */
  updateSessionStats(type, points, campaign = null) {
    this.sessionStats.totalPoints += points;
    
    switch (type) {
      case 'article':
        this.sessionStats.articlesProcessed++;
        this.dailyStats.articlesCompleted++;
        break;
      case 'video':
        this.sessionStats.videosProcessed++;
        this.dailyStats.videosCompleted++;
        break;
      case 'carousel':
        this.sessionStats.carouselsProcessed++;
        this.dailyStats.carouselsCompleted++;
        break;
    }

    // Update daily stats
    this.dailyStats.totalPoints += points;
    
    // Track campaign stats
    if (campaign) {
      if (!this.dailyStats.campaigns[campaign]) {
        this.dailyStats.campaigns[campaign] = { points: 0, count: 0 };
      }
      this.dailyStats.campaigns[campaign].points += points;
      this.dailyStats.campaigns[campaign].count++;
    }

    this.saveDailyStats();
  }

  /**
   * Update error count
   */
  updateErrorCount() {
    this.sessionStats.errors++;
  }

  /**
   * Get session summary
   */
  getSessionSummary() {
    const duration = new Date() - this.sessionStats.startTime;
    const durationFormatted = moment.duration(duration).humanize();

    return {
      duration: durationFormatted,
      totalPoints: this.sessionStats.totalPoints,
      articlesProcessed: this.sessionStats.articlesProcessed,
      videosProcessed: this.sessionStats.videosProcessed,
      carouselsProcessed: this.sessionStats.carouselsProcessed,
      totalProcessed: this.sessionStats.articlesProcessed + 
                     this.sessionStats.videosProcessed + 
                     this.sessionStats.carouselsProcessed,
      errors: this.sessionStats.errors,
      pointsPerMinute: Math.round(this.sessionStats.totalPoints / (duration / 60000))
    };
  }

  /**
   * Get daily summary
   */
  getDailySummary() {
    return {
      date: this.dailyStats.date,
      totalPoints: this.dailyStats.totalPoints,
      dailyExp: this.dailyStats.dailyExp,
      maxDailyExp: config.LIMITS.MAX_DAILY_EXP,
      expProgress: `${this.dailyStats.dailyExp}/${config.LIMITS.MAX_DAILY_EXP}`,
      articlesCompleted: this.dailyStats.articlesCompleted,
      videosCompleted: this.dailyStats.videosCompleted,
      carouselsCompleted: this.dailyStats.carouselsCompleted,
      totalCompleted: this.dailyStats.articlesCompleted + 
                     this.dailyStats.videosCompleted + 
                     this.dailyStats.carouselsCompleted,
      sessionsRun: this.dailyStats.sessionsRun,
      currentTier: this.dailyStats.currentTier,
      campaigns: this.dailyStats.campaigns
    };
  }

  /**
   * Check and update progress
   */
  async checkProgress() {
    try {
      logger.info('📊 Checking current progress...');

      // Get current points and loyalty
      const [currentPoints, loyaltyInfo] = await Promise.all([
        this.getCurrentPoints(),
        this.getCurrentLoyalty()
      ]);

      if (loyaltyInfo) {
        // Update daily EXP
        this.dailyStats.dailyExp = loyaltyInfo.dailyExp;
        
        // Update tier info
        if (!this.dailyStats.startTier) {
          this.dailyStats.startTier = loyaltyInfo.currentTier;
        }
        this.dailyStats.currentTier = loyaltyInfo.currentTier;

        // Log progress
        logger.progressUpdate(currentPoints, loyaltyInfo.dailyExp, loyaltyInfo.currentTier);
        
        // Check if daily limit reached
        if (loyaltyInfo.dailyExp >= loyaltyInfo.maxDailyExp) {
          logger.warn('⚠️ Daily EXP limit reached!');
          return { limitReached: true, loyaltyInfo, currentPoints };
        }

        // Check tier change
        if (this.dailyStats.startTier && this.dailyStats.startTier !== loyaltyInfo.currentTier) {
          logger.success(`🎉 Tier upgraded: ${this.dailyStats.startTier} → ${loyaltyInfo.currentTier}`);
        }
      }

      this.saveDailyStats();
      
      return { limitReached: false, loyaltyInfo, currentPoints };

    } catch (error) {
      logger.error('❌ Failed to check progress', error.message);
      return { limitReached: false, loyaltyInfo: null, currentPoints: 0 };
    }
  }

  /**
   * Start session tracking
   */
  startSession() {
    this.sessionStats.startTime = new Date();
    this.dailyStats.sessionsRun++;
    
    logger.info('🚀 Session started');
    this.saveDailyStats();
  }

  /**
   * End session tracking
   */
  endSession() {
    const summary = this.getSessionSummary();
    
    logger.info('📊 Session Summary:');
    logger.info(`   Duration: ${summary.duration}`);
    logger.info(`   Total Points: ${summary.totalPoints}`);
    logger.info(`   Content Processed: ${summary.totalProcessed} (${summary.articlesProcessed}A, ${summary.videosProcessed}V, ${summary.carouselsProcessed}C)`);
    logger.info(`   Points/Minute: ${summary.pointsPerMinute}`);
    logger.info(`   Errors: ${summary.errors}`);

    this.saveDailyStats();
    return summary;
  }

  /**
   * Log daily summary
   */
  logDailySummary() {
    const summary = this.getDailySummary();
    
    logger.info('📅 Daily Summary:');
    logger.info(`   Date: ${summary.date}`);
    logger.info(`   Total Points: ${summary.totalPoints}`);
    logger.info(`   Daily EXP: ${summary.expProgress}`);
    logger.info(`   Content Completed: ${summary.totalCompleted} (${summary.articlesCompleted}A, ${summary.videosCompleted}V, ${summary.carouselsCompleted}C)`);
    logger.info(`   Sessions Run: ${summary.sessionsRun}`);
    logger.info(`   Current Tier: ${summary.currentTier}`);
    
    if (Object.keys(summary.campaigns).length > 0) {
      logger.info('   Campaign Breakdown:');
      Object.entries(summary.campaigns).forEach(([campaign, stats]) => {
        logger.info(`     ${campaign}: ${stats.points} points (${stats.count} items)`);
      });
    }
  }

  /**
   * Check if should continue processing
   */
  shouldContinue(bypassDailyLimit = false) {
    // Check daily EXP limit (unless bypassed)
    if (!bypassDailyLimit && this.dailyStats.dailyExp >= config.LIMITS.MAX_DAILY_EXP) {
      logger.warn('⚠️ Daily EXP limit reached, stopping');
      return false;
    }

    // Check target daily points
    if (this.dailyStats.totalPoints >= config.LIMITS.TARGET_DAILY_POINTS) {
      logger.info('🎯 Daily points target reached!');
      return false;
    }

    return true;
  }
}

module.exports = ProgressService;
