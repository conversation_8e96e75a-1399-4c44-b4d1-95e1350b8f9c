/**
 * Test AllAccess Authentication Flow
 * Test the complete AllAccess authentication flow
 */

const axios = require('axios');
const { URL } = require('url');

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
  },
  AUTH: {
    NAME: 'amild',
    SECRET_KEY: '8Q1kUKMlUZUsCntnclnPF+DDQ+k1HY0b1UNGesHK',
    APP_KEY: '629DE67FBE4A67B65541',
    DEVICE_ID: 'meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r',
    DEVICE_TYPE: 'website'
  }
};

async function testAllAccessFlow() {
  console.log('🧪 Testing AllAccess Authentication Flow');
  console.log('=========================================');
  
  try {
    // Step 1: Generate Token
    console.log('1. Generating token...');
    
    const tokenResponse = await axios.post(`${config.API.BASE_URL}/api/token/get`, {
      name: config.AUTH.NAME,
      secret_key: config.AUTH.SECRET_KEY,
      device_type: config.AUTH.DEVICE_TYPE,
      app_key: config.AUTH.APP_KEY,
      device_id: config.AUTH.DEVICE_ID
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (tokenResponse.data.code !== 200) {
      throw new Error('Token generation failed');
    }
    
    const token = tokenResponse.data.data.token.token_code;
    console.log(`✅ Token: ${token.substring(0, 20)}...`);
    
    // Step 2: Request AllAccess URL
    console.log('\n2. Requesting AllAccess URL...');
    
    const reqUrlData = {
      ga: "2.62775660.1964620225.1752408888-214340915.1752408888",
      gl: "1*hmudcy*_ga*MjE0MzQwOTE1LjE3NTI0MDg4ODg.*_ga_Q0M8VSMG8Z*czE3NTI0MDg4ODkkbzEkZzEkdDE3NTI0MDg5MDgkajQxJGwwJGgw",
      detail_page: "https://amild.id/br",
      first_page: "login",
      platform: "amild x all",
      auth_data: "",
      ref: "",
      redirect_url: "https://amild.id/allaccess-loading",
      utm: ""
    };
    
    const reqUrlResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/req-url`, reqUrlData, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': token,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (reqUrlResponse.data.code !== 200) {
      throw new Error('AllAccess URL request failed');
    }
    
    const allAccessUrl = reqUrlResponse.data.data.data.url;
    console.log(`✅ AllAccess URL: ${allAccessUrl.substring(0, 100)}...`);
    
    // Step 3: Parse AllAccess URL
    console.log('\n3. Parsing AllAccess URL...');
    
    const parsedUrl = new URL(allAccessUrl);
    const authProfile = parsedUrl.searchParams.get('auth_profile');
    const ga = parsedUrl.searchParams.get('_ga');
    const gl = parsedUrl.searchParams.get('_gl');
    
    console.log(`✅ Host: ${parsedUrl.host}`);
    console.log(`✅ Pathname: ${parsedUrl.pathname}`);
    console.log(`✅ Auth Profile Length: ${authProfile?.length || 0}`);
    console.log(`✅ GA: ${ga}`);
    console.log(`✅ GL: ${gl?.substring(0, 50)}...`);
    
    // Step 4: Analyze the authentication requirements
    console.log('\n4. Analyzing authentication requirements...');
    
    console.log('📋 AllAccess Authentication Flow Analysis:');
    console.log('   ┌─ Step 1: Visit AllAccess login page');
    console.log('   ├─ Step 2: Extract CSRF token from page');
    console.log('   ├─ Step 3: Submit login form with:');
    console.log('   │  ├─ allaccess_csrf: [from page]');
    console.log('   │  ├─ trackId: [generated]');
    console.log('   │  ├─ platform: "amild x All"');
    console.log('   │  ├─ username: [user email]');
    console.log('   │  └─ g-recaptcha-response: [reCAPTCHA]');
    console.log('   ├─ Step 4: Handle login response');
    console.log('   ├─ Step 5: Submit JWT callback with:');
    console.log('   │  ├─ allaccess_csrf: [same token]');
    console.log('   │  ├─ id_token: [JWT from Gigya]');
    console.log('   │  ├─ consumerId: [user ID]');
    console.log('   │  └─ other parameters');
    console.log('   └─ Step 6: Get redirect URL back to amild.id');
    
    // Step 5: Check if we can get login page
    console.log('\n5. Testing AllAccess login page access...');
    
    try {
      const loginPageResponse = await axios.get(allAccessUrl, {
        headers: {
          'User-Agent': config.API.USER_AGENT,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Dest': 'document'
        }
      });
      
      console.log(`✅ Login page accessible (${loginPageResponse.status})`);
      console.log(`✅ Content length: ${loginPageResponse.data.length}`);
      
      // Try to extract CSRF token
      const csrfMatch = loginPageResponse.data.match(/name="allaccess_csrf"[^>]*value="([^"]+)"/);
      if (csrfMatch) {
        console.log(`✅ CSRF token found: ${csrfMatch[1]}`);
      } else {
        console.log('⚠️ CSRF token not found in page');
      }
      
      // Check for reCAPTCHA
      const recaptchaMatch = loginPageResponse.data.match(/data-sitekey="([^"]+)"/);
      if (recaptchaMatch) {
        console.log(`⚠️ reCAPTCHA required (sitekey: ${recaptchaMatch[1]})`);
      }
      
    } catch (pageError) {
      console.log('❌ Login page access failed:', pageError.message);
    }
    
    console.log('\n📋 Summary:');
    console.log('✅ Token generation: SUCCESS');
    console.log('✅ AllAccess URL request: SUCCESS');
    console.log('✅ URL parsing: SUCCESS');
    console.log('⚠️ Full authentication: REQUIRES MANUAL INTERVENTION');
    console.log('');
    console.log('🔧 Manual Steps Required:');
    console.log(`1. Open: ${allAccessUrl}`);
    console.log('2. Login with your email and password');
    console.log('3. Complete reCAPTCHA verification');
    console.log('4. Wait for redirect to amild.id');
    console.log('5. Check if login status becomes active');
    console.log('');
    console.log('💡 Alternative: Use existing session cookies if available');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log('Error status:', error.response.status);
      console.log('Error data:', error.response.data);
    }
  }
}

testAllAccessFlow();
