# Amild Auto Point Farming Bot

🤖 **Automated point farming bot for Amild.id platform** - Otomatis farming poin untuk naik tier dan maksimalkan reward!

## 🎯 Fitur Utama

### ✨ **Smart Content Processing**
- **Auto Article Reading** - Simulasi membaca artikel dengan tracking natural
- **Auto Video Watching** - Simulasi menonton video berdasarkan durasi real
- **Auto Carousel Completion** - Otomatis menyelesaikan carousel engagement
- **Intelligent Tracking** - Mimic human behavior untuk avoid detection

### 🧠 **Smart Strategy Engine**
- **Priority-based Processing** - Prioritas berdasarkan poin dan campaign value
- **Time Optimization** - Optimasi berdasarkan waktu yang tersedia
- **Campaign Targeting** - Target campaign high-value (gac-2024: 150 poin, inspiraksi2025: 100 poin)
- **Daily Limit Management** - Kelola unlimited content vs daily limited content

### 📊 **Progress Monitoring**
- **Real-time Point Tracking** - Monitor poin yang didapat secara real-time
- **Tier Progression** - Track progress tier (Silver → Gold → higher)
- **Daily EXP Monitoring** - Monitor daily EXP (0/250) dan daily limits
- **Session Statistics** - Laporan lengkap per session dan harian

### 🔐 **Authentication System**
- **Auto Login** - Otomatis login dengan kredensial
- **Token Management** - Auto refresh token ketika expired
- **Session Maintenance** - Maintain session dengan periodic checks

## 🚀 Installation

1. **Clone repository:**
```bash
git clone <repository-url>
cd amild-auto-farming-bot
```

2. **Install dependencies:**
```bash
npm install
```

3. **Configure credentials** (edit `config.js` if needed):
```javascript
AUTH: {
  NAME: 'amild',
  SECRET_KEY: '8Q1kUKMlUZUsCntnclnPF+DDQ+k1HY0b1UNGesHK',
  APP_KEY: '629DE67FBE4A67B65541',
  DEVICE_ID: 'meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r'
}
```

## 🎮 Usage

### **Quick Mode** (Fast Points)
```bash
npm start quick [maxItems] [maxTimeMinutes]

# Examples:
npm start quick 20 10    # Process 20 items in 10 minutes
npm start quick          # Default: 20 items
```

### **Full Mode** (Complete Farming)
```bash
npm start full [maxTimeMinutes]

# Examples:
npm start full 60        # Full session for 60 minutes
npm start full           # Full session (no time limit)
```

### **Development Mode**
```bash
npm run dev              # Run with nodemon for development
```

## 📋 Strategy Priorities

Bot menggunakan strategy cerdas berdasarkan prioritas:

### **🏆 Priority 1: GAC-2024 Videos (150 points)**
- Video campaign gac-2024
- Highest point value
- Duration-based processing

### **🥇 Priority 2: Unlimited High-Value (100+ points)**
- Content dengan `is_exclude_daily_limit: 1`
- Campaign: inspiraksi2025, go-ahead-music
- Tidak terkena daily limit

### **🥈 Priority 3: Quick Carousels (50-100 points)**
- Carousel engagement (fastest processing)
- Quick points dengan minimal time investment

### **🥉 Priority 4: Remaining High-Value (100+ points)**
- Artikel dan content lain dengan 100+ poin
- Campaign: why-not, dll

### **📝 Priority 5: Medium-Value (50-99 points)**
- Content dengan poin sedang
- Diproses jika waktu masih tersedia

## 📊 Content Categories

| Category | Items | Priority | Avg Points |
|----------|-------|----------|------------|
| Music | 32 items | ⭐⭐⭐⭐⭐ | 100 points |
| Daily Life | 15 items | ⭐⭐⭐ | 50-100 points |
| Travel | 9 items | ⭐⭐⭐ | 50-150 points |
| Lifestyle | 8 items | ⭐⭐⭐⭐ | 100 points |
| Art | 5 items | ⭐⭐ | 50 points |

## 🔧 Configuration

Edit `config.js` untuk customize behavior:

```javascript
// Timing Configuration
TIMING: {
  ARTICLE_READ_TIME_PER_CHAR: 50,    // Reading speed
  VIDEO_WATCH_MULTIPLIER: 1.0,       // Watch full duration
  CAROUSEL_COMPLETE_TIME: 5000,      // 5 seconds per carousel
  REQUEST_DELAY_MIN: 1000,           // Min delay between requests
  REQUEST_DELAY_MAX: 3000            // Max delay between requests
}

// Daily Limits
LIMITS: {
  MAX_DAILY_EXP: 250,               // Daily EXP limit
  TARGET_DAILY_POINTS: 2000,        // Target points per day
  MAX_CONTENT_PER_CATEGORY: 50      // Safety limit
}
```

## 📈 Expected Results

### **Daily Targets:**
- **Daily EXP:** 250/250 (maximum)
- **Total Points:** 2000+ points per day
- **Content Processed:** 50-100 items per session
- **Tier Progression:** Silver → Gold → higher tiers

### **Session Performance:**
- **Quick Mode:** 500-1000 points in 10-15 minutes
- **Full Mode:** 2000+ points in 1-2 hours
- **Success Rate:** 90%+ dengan proper error handling

## 🛡️ Safety Features

- **Rate Limiting** - Delay antar request untuk avoid detection
- **Human-like Behavior** - Realistic timing dan interaction patterns
- **Error Recovery** - Auto retry dengan exponential backoff
- **Daily Limit Respect** - Stop ketika daily limit tercapai
- **Session Management** - Proper login/logout handling

## 📝 Logs & Monitoring

Bot menyimpan logs dan statistics:

- **Console Logs** - Real-time colored output
- **File Logs** - `bot.log` untuk debugging
- **Daily Stats** - `stats_YYYY-MM-DD.json` untuk tracking harian
- **Progress Monitoring** - Real-time point dan tier tracking

## 🚨 Troubleshooting

### **Authentication Issues:**

Bot menggunakan **complex authentication flow** yang melibatkan AllAccess platform:

1. **Generate Token** → `POST /api/token/get`
2. **Check Login Status** → `POST /api/fs/user/login-status`
3. **Request AllAccess URL** → `POST /api/fs/user/req-url`
4. **AllAccess Login** → Manual login dengan reCAPTCHA
5. **JWT Callback** → Automatic redirect back to Amild

### **Quick Fix - Bypass Authentication:**

```bash
# Try bypass authentication first
npm run bypass

# If bypass finds working token, update config.js
# Then run bot normally
npm start quick
```

### **Manual Authentication Steps:**

Jika bypass gagal, lakukan manual authentication:

```bash
# 1. Get AllAccess URL
npm run test-allaccess

# 2. Open URL yang diberikan di browser
# 3. Login dengan email/password + reCAPTCHA
# 4. Wait for redirect ke amild.id
# 5. Extract token dari network tab
# 6. Update config.js dengan token baru
```

### **Common Issues:**

1. **Login Status Inactive**
   - Run `npm run bypass` untuk cari token aktif
   - Atau lakukan manual authentication

2. **Token Expired**
   - Generate token baru dengan `npm run test-auth`
   - Update DEVICE_ID di config.js

3. **AllAccess Flow Required**
   - Run `npm run test-allaccess` untuk manual steps
   - Complete reCAPTCHA verification

4. **No Content Found**
   - Website mungkin down (tunggu beberapa jam)
   - Check authentication status

5. **Rate Limited**
   - Increase delays di config
   - Reduce concurrent processing

6. **Daily Limit Reached**
   - Normal behavior, tunggu reset harian
   - Check `stats_*.json` untuk details

## 📞 Support

Jika ada masalah atau pertanyaan:
1. Check logs di `bot.log`
2. Review daily stats di `stats_*.json`
3. Adjust configuration di `config.js`

---

**⚠️ Disclaimer:** Bot ini dibuat untuk educational purposes. Gunakan dengan bijak dan sesuai terms of service platform.

**🎉 Happy Farming!** 🚀
