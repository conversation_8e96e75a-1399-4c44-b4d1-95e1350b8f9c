/**
 * Authentication Service
 * Handles token generation, login status, and session management
 */

const axios = require('axios');
const config = require('../config');
const logger = require('../utils/logger');
const helpers = require('../utils/helpers');

class AuthService {
  constructor() {
    this.token = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.userProfile = null;
    this.isLoggedIn = false;
  }

  /**
   * Generate authentication token
   */
  async generateToken() {
    try {
      logger.info('🔑 Generating authentication token...');
      
      const requestData = {
        name: config.AUTH.NAME,
        secret_key: config.AUTH.SECRET_KEY,
        device_type: config.AUTH.DEVICE_TYPE,
        app_key: config.AUTH.APP_KEY,
        device_id: config.AUTH.DEVICE_ID
      };

      logger.debug('Token request data:', { ...requestData, secret_key: '***' });

      const response = await axios.post(`${config.API.BASE_URL}/api/token/get`, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Accept-Encoding': 'gzip, deflate, br',
          'Priority': 'u=1, i'
        }
      });

      if (response.data.code === 200 && response.data.data.token) {
        const tokenData = response.data.data.token;
        
        this.token = tokenData.token_code;
        this.refreshToken = tokenData.refresh_token;
        this.tokenExpiry = new Date(tokenData.expired_date);
        
        logger.success('✅ Token generated successfully');
        logger.debug('Token details', {
          token: this.token.substring(0, 10) + '...',
          expiry: this.tokenExpiry
        });
        
        return true;
      } else {
        throw new Error('Invalid token response');
      }
    } catch (error) {
      logger.error('❌ Failed to generate token', error.message);
      throw error;
    }
  }

  /**
   * Check login status
   */
  async checkLoginStatus() {
    try {
      if (!this.token) {
        throw new Error('No token available');
      }

      logger.debug(`🔍 Checking login status with token: ${this.token.substring(0, 10)}...`);

      const response = await axios.post(`${config.API.BASE_URL}/api/fs/user/login-status`, {}, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': this.token,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Accept-Encoding': 'gzip, deflate, br',
          'Priority': 'u=1, i'
        }
      });

      logger.debug('Login status response:', response.data);

      if (response.data.code === 200) {
        this.isLoggedIn = response.data.data.data.login_status;

        if (this.isLoggedIn) {
          logger.debug('✅ Login status: Active');
        } else {
          logger.warn('⚠️ Login status: Inactive');
          logger.debug('Full response data:', JSON.stringify(response.data, null, 2));
        }

        return this.isLoggedIn;
      } else {
        throw new Error(`Invalid login status response: ${response.data.code}`);
      }
    } catch (error) {
      logger.error('❌ Failed to check login status', error.message);
      if (error.response) {
        logger.debug('Error response:', error.response.data);
      }
      this.isLoggedIn = false;
      return false;
    }
  }

  /**
   * Get user profile
   */
  async getUserProfile() {
    try {
      if (!this.token) {
        throw new Error('No token available');
      }

      logger.info('👤 Fetching user profile...');

      const response = await axios.post(`${config.API.BASE_URL}/api/fs/user/get-profile`, {}, {
        headers: this.getAuthHeaders()
      });

      if (response.data.code === 200) {
        this.userProfile = response.data.data.data;
        
        logger.success('✅ User profile loaded');
        logger.info(`User: ${this.userProfile.fullname} (${this.userProfile.email})`);
        logger.info(`Tier: ${this.userProfile.tier_id} | Status: ${this.userProfile.status_tier}`);
        
        return this.userProfile;
      } else {
        throw new Error(`Invalid profile response: ${response.data.code}`);
      }
    } catch (error) {
      logger.error('❌ Failed to get user profile', error.message);
      if (error.response) {
        logger.debug('Profile error response:', error.response.data);
      }
      throw error;
    }
  }

  /**
   * Complete AllAccess authentication flow
   */
  async completeAllAccessFlow() {
    try {
      logger.info('🔐 Starting AllAccess authentication flow...');

      // Step 1: Request AllAccess URL
      const urlData = await this.requestAllAccessUrl();

      // Step 2: Parse the AllAccess URL to get auth_profile
      const allAccessUrl = new URL(urlData.url);
      const authProfile = allAccessUrl.searchParams.get('auth_profile');

      if (!authProfile) {
        throw new Error('No auth_profile found in AllAccess URL');
      }

      logger.debug(`🔗 Auth Profile: ${authProfile.substring(0, 50)}...`);

      // Step 3: Simulate visiting AllAccess login page
      // In a real implementation, this would involve:
      // - Getting CSRF token from the login page
      // - Handling reCAPTCHA (which is complex for automation)
      // - Submitting login form
      // - Handling JWT callback

      // For now, we'll mark this as a complex flow that requires manual intervention
      logger.warn('⚠️ AllAccess flow requires manual authentication (reCAPTCHA + login)');
      logger.info('🔗 AllAccess URL for manual login:');
      logger.info(`   ${urlData.url.substring(0, 100)}...`);

      // Return the URL for manual processing
      return {
        success: false,
        requiresManualAuth: true,
        allAccessUrl: urlData.url,
        authProfile: authProfile
      };

    } catch (error) {
      logger.error('❌ AllAccess flow failed', error.message);
      throw error;
    }
  }

  /**
   * Request AllAccess URL
   */
  async requestAllAccessUrl() {
    try {
      if (!this.token) {
        throw new Error('No token available');
      }

      logger.debug('🔗 Requesting AllAccess URL...');

      const requestData = {
        ga: "2.62775660.1964620225.1752408888-214340915.1752408888",
        gl: "1*hmudcy*_ga*MjE0MzQwOTE1LjE3NTI0MDg4ODg.*_ga_Q0M8VSMG8Z*czE3NTI0MDg4ODkkbzEkZzEkdDE3NTI0MDg5MDgkajQxJGwwJGgw",
        detail_page: "https://amild.id/br",
        first_page: "login",
        platform: "amild x all",
        auth_data: "",
        ref: "",
        redirect_url: "https://amild.id/allaccess-loading",
        utm: ""
      };

      const response = await axios.post(`${config.API.BASE_URL}/api/fs/user/req-url`, requestData, {
        headers: this.getAuthHeaders()
      });

      if (response.data.code === 200) {
        const urlData = response.data.data.data;
        logger.debug('✅ AllAccess URL received');
        logger.debug(`   URL: ${urlData.url.substring(0, 100)}...`);
        logger.debug(`   Platform: ${urlData.platform}`);
        logger.debug(`   Visit AllAccess: ${urlData.visit_allaccess}`);

        return urlData;
      } else {
        throw new Error('Invalid AllAccess URL response');
      }
    } catch (error) {
      logger.error('❌ Failed to get AllAccess URL', error.message);
      if (error.response) {
        logger.debug('AllAccess URL error response:', error.response.data);
      }
      throw error;
    }
  }

  /**
   * Get loyalty information
   */
  async getLoyaltyInfo() {
    try {
      if (!this.token) {
        throw new Error('No token available');
      }

      const response = await axios.post(`${config.API.BASE_URL}/api/fs/user/loyalty-teaser`, {}, {
        headers: this.getAuthHeaders()
      });

      if (response.data.code === 200) {
        const loyaltyData = response.data.data.data;
        
        logger.info(`📊 Loyalty Info - EXP: ${loyaltyData.accumulated_exp} | Daily: ${loyaltyData.daily_exp}/${loyaltyData.max_daily_exp}`);
        logger.info(`🏆 Current Tier: ${loyaltyData.current_tier} → Next: ${loyaltyData.next_tier}`);
        
        return loyaltyData;
      } else {
        throw new Error('Invalid loyalty response');
      }
    } catch (error) {
      logger.error('❌ Failed to get loyalty info', error.message);
      throw error;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired() {
    if (!this.tokenExpiry) return true;
    return new Date() >= this.tokenExpiry;
  }

  /**
   * Initialize authentication (full login process)
   */
  async initialize() {
    try {
      logger.info('🚀 Initializing authentication...');

      // Check if we have a working token in config
      if (config.AUTH.WORKING_TOKEN) {
        logger.info('🔑 Using working token from config...');
        this.token = config.AUTH.WORKING_TOKEN;
        this.tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // Assume 24h expiry

        // Test the working token immediately
        const loginStatus = await this.checkLoginStatus();
        if (loginStatus) {
          logger.success('✅ Working token is still active');

          // Get user profile and loyalty info
          await this.getUserProfile();
          await this.getLoyaltyInfo();

          logger.success('✅ Authentication initialized successfully with working token');
          return true;
        } else {
          logger.warn('⚠️ Working token is no longer active, generating new token...');
        }
      }

      // Generate token if no working token or working token failed
      await this.generateToken();

      // Add delay after token generation
      logger.debug('⏳ Waiting for token to become active...');
      await helpers.sleep(2000);

      // Check login status with retry
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        await this.checkLoginStatus();

        if (this.isLoggedIn) {
          break;
        }

        retryCount++;
        if (retryCount < maxRetries) {
          logger.warn(`⚠️ Login status inactive, retrying... (${retryCount}/${maxRetries})`);
          await helpers.sleep(3000); // Wait 3 seconds before retry
        }
      }

      if (!this.isLoggedIn) {
        // Try to regenerate token once more
        logger.warn('🔄 Attempting token regeneration...');
        await this.generateToken();
        await helpers.sleep(2000);
        await this.checkLoginStatus();

        if (!this.isLoggedIn) {
          // Last resort: try with a different approach
          logger.warn('🔧 Trying alternative authentication approach...');

          // Try to request AllAccess URL to verify token validity
          try {
            await this.requestAllAccessUrl();
            logger.info('✅ AllAccess URL request successful, token appears valid');
            this.isLoggedIn = true; // Override login status if AllAccess request succeeds
          } catch (allAccessError) {
            logger.debug('AllAccess URL request failed, trying profile...');

            // Sometimes the login status endpoint returns false even when token is valid
            // Let's try to get user profile directly to verify token validity
            try {
              await this.getUserProfile();
              logger.info('✅ Token appears to be valid despite login status check');
              this.isLoggedIn = true; // Override login status if profile fetch succeeds
            } catch (profileError) {
              logger.error('❌ Token validation failed', profileError.message);

              // Try the complete AllAccess flow as last resort
              logger.warn('🔄 Attempting complete AllAccess authentication flow...');
              try {
                const allAccessResult = await this.completeAllAccessFlow();
                if (allAccessResult.requiresManualAuth) {
                  logger.warn('⚠️ Manual authentication required');
                  logger.info('📋 Please complete authentication manually:');
                  logger.info(`   1. Open: ${allAccessResult.allAccessUrl.substring(0, 100)}...`);
                  logger.info('   2. Login with your credentials');
                  logger.info('   3. Complete reCAPTCHA verification');
                  logger.info('   4. Wait for redirect to amild.id');
                  logger.info('   5. Then restart the bot');

                  throw new Error('Manual authentication required - please complete AllAccess login');
                }
              } catch (flowError) {
                logger.error('❌ AllAccess flow failed', flowError.message);
                throw new Error('Authentication failed: All methods exhausted');
              }
            }
          }
        }
      }

      // Get user profile and loyalty info
      await this.getUserProfile();
      await this.getLoyaltyInfo();

      logger.success('✅ Authentication initialized successfully');
      return true;

    } catch (error) {
      logger.error('❌ Authentication initialization failed', error.message);
      throw error;
    }
  }

  /**
   * Refresh authentication if needed
   */
  async refreshIfNeeded() {
    try {
      // Check if token is expired
      if (this.isTokenExpired()) {
        logger.warn('⚠️ Token expired, refreshing...');
        await this.generateToken();
      }
      
      // Check login status
      const isLoggedIn = await this.checkLoginStatus();
      
      if (!isLoggedIn) {
        logger.warn('⚠️ Not logged in, re-initializing...');
        await this.initialize();
      }
      
      return true;
    } catch (error) {
      logger.error('❌ Failed to refresh authentication', error.message);
      throw error;
    }
  }

  /**
   * Get current authentication headers
   */
  getAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      'Standardized-Token': this.token,
      'User-Agent': config.API.USER_AGENT,
      'Origin': config.API.ORIGIN,
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Sec-Ch-Ua-Platform': '"Windows"',
      'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
      'Sec-Ch-Ua-Mobile': '?0',
      'Sec-Fetch-Site': 'cross-site',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Dest': 'empty',
      'Accept-Encoding': 'gzip, deflate, br',
      'Priority': 'u=1, i'
    };
  }
}

module.exports = AuthService;
