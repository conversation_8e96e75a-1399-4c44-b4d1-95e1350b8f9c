/**
 * Find Missing Endpoint for Point Claiming
 * Try to find the missing endpoint for claiming points after article completion
 */

const axios = require('axios');

const config_file = require('./config');
const WORKING_TOKEN = config_file.AUTH.WORKING_TOKEN;

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
  }
};

async function findMissingEndpoint() {
  console.log('🔍 Finding Missing Point Claiming Endpoint');
  console.log('==========================================');
  
  try {
    // Get an unlimited article
    const contentResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
      is_regular: 1,
      campaign: '',
      category: '',
      sub_category: 'music',
      is_branded: 1,
      format: 'article',
      is_highlight: '',
      pinned: '',
      page: 0,
      length: 5,
      is_engagement: '',
      string_id: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    const articles = contentResponse.data.data.items.filter(item => 
      item.is_exclude_daily_limit === 1 && 
      item.finished !== 1
    );
    
    if (articles.length === 0) {
      console.log('❌ No unlimited articles available');
      return;
    }
    
    const testArticle = articles[0];
    console.log(`\n📄 Testing article: ${testArticle.title}`);
    console.log(`   ID: ${testArticle.id}`);
    console.log(`   Points: ${testArticle.point}`);
    
    // Try different potential endpoints
    const potentialEndpoints = [
      // Based on carousel pattern
      {
        name: 'simple_engagement/answer_article',
        endpoint: '/api/fs/simple_engagement/answer_article',
        data: { content_id: parseInt(testArticle.id) }
      },
      {
        name: 'simple_engagement/complete_article',
        endpoint: '/api/fs/simple_engagement/complete_article',
        data: { content_id: parseInt(testArticle.id) }
      },
      {
        name: 'simple_engagement/finish_article',
        endpoint: '/api/fs/simple_engagement/finish_article',
        data: { content_id: parseInt(testArticle.id) }
      },
      // Based on feed pattern
      {
        name: 'feed/complete',
        endpoint: '/api/fs/feed/complete',
        data: { 
          content_id: parseInt(testArticle.id),
          content_type: 'article'
        }
      },
      {
        name: 'feed/finish',
        endpoint: '/api/fs/feed/finish',
        data: { 
          content_id: parseInt(testArticle.id),
          format: 'article'
        }
      },
      // Based on engagement pattern
      {
        name: 'engagement/complete',
        endpoint: '/api/fs/engagement/complete',
        data: { 
          content_id: parseInt(testArticle.id),
          engagement_type: 'article'
        }
      },
      // Try with string_id
      {
        name: 'simple_engagement with string_id',
        endpoint: '/api/fs/simple_engagement/answer_article',
        data: { 
          content_id: parseInt(testArticle.id),
          string_id: testArticle.string_id
        }
      },
      // Try with campaign
      {
        name: 'simple_engagement with campaign',
        endpoint: '/api/fs/simple_engagement/answer_article',
        data: { 
          content_id: parseInt(testArticle.id),
          campaign: testArticle.campaign_string_id
        }
      }
    ];
    
    console.log('\n🧪 Testing potential endpoints...');
    
    for (const test of potentialEndpoints) {
      try {
        console.log(`\n   Testing: ${test.name}`);
        console.log(`   Endpoint: ${test.endpoint}`);
        console.log(`   Data:`, test.data);
        
        const response = await axios.post(`${config.API.BASE_URL}${test.endpoint}`, test.data, {
          headers: {
            'Content-Type': 'application/json',
            'Standardized-Token': WORKING_TOKEN,
            'User-Agent': config.API.USER_AGENT,
            'Origin': config.API.ORIGIN,
            'Accept': '*/*'
          }
        });
        
        console.log(`   ✅ Response: ${response.status} - ${response.data.code}`);
        if (response.data.code === 200) {
          console.log(`   🎉 POTENTIAL SUCCESS! Data:`, response.data);
        } else {
          console.log(`   ⚠️ Response:`, response.data);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        if (error.response) {
          console.log(`   ❌ ${error.response.status}: ${error.response.data?.message || 'Unknown error'}`);
        } else {
          console.log(`   ❌ Network error: ${error.message}`);
        }
      }
    }
    
    // Try to find pattern in carousel endpoint
    console.log('\n🎠 Checking carousel endpoint for pattern...');
    
    const carouselResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
      is_regular: 1,
      campaign: '',
      category: '',
      sub_category: '',
      is_branded: 1,
      format: 'carousel',
      is_highlight: '',
      pinned: '',
      page: 0,
      length: 5,
      is_engagement: 1,
      string_id: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    const carousels = carouselResponse.data.data.items.filter(item => 
      item.is_engagement === 1 && 
      item.finished !== 1
    );
    
    if (carousels.length > 0) {
      const testCarousel = carousels[0];
      console.log(`\n🎠 Testing carousel: ${testCarousel.title}`);
      console.log(`   Engagement UUID: ${testCarousel.engagement_uuid}`);
      
      // Test carousel endpoint for comparison
      try {
        const carouselTest = await axios.post(`${config.API.BASE_URL}/api/fs/simple_engagement/answer_carousel`, {
          engagement_uuid: testCarousel.engagement_uuid
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Standardized-Token': WORKING_TOKEN,
            'User-Agent': config.API.USER_AGENT,
            'Origin': config.API.ORIGIN,
            'Accept': '*/*'
          }
        });
        
        console.log(`   ✅ Carousel endpoint works: ${carouselTest.data.code}`);
        console.log(`   📋 Carousel response structure:`, carouselTest.data);
        
      } catch (error) {
        console.log(`   ❌ Carousel test failed: ${error.message}`);
      }
    }
    
    console.log('\n💡 Recommendations:');
    console.log('1. Check browser network tab when manually completing article');
    console.log('2. Look for POST requests after article completion');
    console.log('3. Article might not have completion endpoint (points awarded automatically)');
    console.log('4. Points might be delayed or require different trigger');
    
  } catch (error) {
    console.log('❌ Search failed:', error.message);
    if (error.response) {
      console.log('Error data:', error.response.data);
    }
  }
}

findMissingEndpoint();
