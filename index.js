/**
 * Amild Auto Point Farming Bot
 * Main bot controller that orchestrates all systems and handles error recovery
 */

const config = require('./config');
const logger = require('./utils/logger');
const helpers = require('./utils/helpers');

// Services
const AuthService = require('./services/auth');
const ContentService = require('./services/content');
const TrackingService = require('./services/tracking');
const ProgressService = require('./services/progress');
const StrategyService = require('./services/strategy');

// Processors
const ArticleProcessor = require('./processors/article');
const VideoProcessor = require('./processors/video');
const CarouselProcessor = require('./processors/carousel');

class AmildBot {
  constructor() {
    // Initialize services
    this.auth = new AuthService();
    this.content = new ContentService(this.auth);
    this.tracking = new TrackingService(this.auth);
    this.progress = new ProgressService(this.auth);
    this.strategy = new StrategyService(this.progress);

    // Initialize processors
    this.articleProcessor = new ArticleProcessor(this.auth, this.tracking);
    this.videoProcessor = new VideoProcessor(this.auth, this.tracking);
    this.carouselProcessor = new CarouselProcessor(this.auth, this.tracking);

    this.isRunning = false;
    this.shouldStop = false;
  }

  /**
   * Initialize bot
   */
  async initialize(options = {}) {
    try {
      logger.botStart();
      logger.info('🔧 Initializing Amild Bot...');

      // Initialize authentication
      await this.auth.initialize();

      // Start progress tracking
      this.progress.startSession();

      // Check initial progress
      const progressCheck = await this.progress.checkProgress();

      if (progressCheck.limitReached && !options.bypassDailyLimit) {
        logger.warn('⚠️ Daily limit already reached, exiting');
        logger.info('💡 Use "npm start unlimited" to process unlimited content only');
        return false;
      }

      logger.success('✅ Bot initialized successfully');
      return true;

    } catch (error) {
      logger.error('❌ Bot initialization failed', error.message);
      throw error;
    }
  }

  /**
   * Discover and analyze content
   */
  async discoverContent() {
    try {
      logger.info('🔍 Starting content discovery...');

      // Discover all available content
      const allContent = await this.content.discoverAllContent();

      if (allContent.length === 0) {
        logger.warn('⚠️ No content found to process');
        return null;
      }

      logger.success(`✅ Content discovery complete: ${allContent.length} items found`);
      return allContent;

    } catch (error) {
      logger.error('❌ Content discovery failed', error.message);
      throw error;
    }
  }

  /**
   * Process content based on strategy
   */
  async processContent(allContent, options = {}) {
    try {
      logger.info('🚀 Starting content processing...');

      // Create processing strategy
      const strategy = this.strategy.createStrategy(allContent, options);

      // Optimize for time if specified
      const finalStrategy = options.maxTimeMs ? 
        this.strategy.optimizeForTime(strategy, options.maxTimeMs) : 
        strategy;

      let totalResults = {
        processed: 0,
        successful: 0,
        failed: 0,
        totalPoints: 0
      };

      // Process each phase
      for (const phase of finalStrategy.phases) {
        if (this.shouldStop || !this.progress.shouldContinue(options.bypassDailyLimit)) {
          logger.info('🛑 Stopping processing due to limits or user request');
          break;
        }

        logger.info(`📋 Processing Phase: ${phase.name} (${phase.content.length} items)`);

        let phaseResults;

        // Process based on content type
        const contentByType = this.groupContentByType(phase.content);

        // Process videos first (highest value)
        if (contentByType.video.length > 0) {
          phaseResults = await this.videoProcessor.processVideos(contentByType.video);
          this.updateTotalResults(totalResults, phaseResults, 'video');
        }

        // Process carousels (fastest)
        if (contentByType.carousel.length > 0 && this.progress.shouldContinue(options.bypassDailyLimit)) {
          phaseResults = await this.carouselProcessor.processCarousels(contentByType.carousel);
          this.updateTotalResults(totalResults, phaseResults, 'carousel');
        }

        // Process articles
        if (contentByType.article.length > 0 && this.progress.shouldContinue(options.bypassDailyLimit)) {
          phaseResults = await this.articleProcessor.processArticles(contentByType.article);
          this.updateTotalResults(totalResults, phaseResults, 'article');
        }

        // Check progress after each phase
        await this.progress.checkProgress();

        logger.success(`✅ Phase completed: ${phase.name}`);
      }

      return totalResults;

    } catch (error) {
      logger.error('❌ Content processing failed', error.message);
      throw error;
    }
  }

  /**
   * Group content by type
   */
  groupContentByType(content) {
    return {
      video: content.filter(c => c.format === 'video'),
      article: content.filter(c => c.format === 'article'),
      carousel: content.filter(c => c.format === 'carousel' && c.is_engagement === 1)
    };
  }

  /**
   * Update total results
   */
  updateTotalResults(totalResults, phaseResults, type) {
    totalResults.processed += phaseResults.processed;
    totalResults.successful += phaseResults.successful;
    totalResults.failed += phaseResults.failed;
    totalResults.totalPoints += phaseResults.totalPoints;

    // Update progress tracking
    if (phaseResults.successful > 0) {
      // This is a simplified update - in real implementation, 
      // each processor should call progress.updateSessionStats individually
      for (let i = 0; i < phaseResults.successful; i++) {
        const avgPoints = Math.floor(phaseResults.totalPoints / phaseResults.successful);
        this.progress.updateSessionStats(type, avgPoints);
      }
    }
  }

  /**
   * Run quick farming session
   */
  async runQuickSession(maxItems = 20) {
    try {
      logger.info(`⚡ Starting quick farming session (${maxItems} items)`);

      const allContent = await this.discoverContent();
      if (!allContent) return;

      const quickStrategy = this.strategy.createQuickStrategy(allContent, maxItems);
      
      return await this.processContent(allContent, { 
        strategy: quickStrategy,
        maxTimeMs: 10 * 60 * 1000 // 10 minutes
      });

    } catch (error) {
      logger.error('❌ Quick session failed', error.message);
      throw error;
    }
  }

  /**
   * Run full farming session
   */
  async runFullSession(options = {}) {
    try {
      logger.info('🎯 Starting full farming session');

      const allContent = await this.discoverContent();
      if (!allContent) return;

      return await this.processContent(allContent, {
        includeAllContent: true,
        maxTimeMs: options.maxTimeMs
      });

    } catch (error) {
      logger.error('❌ Full session failed', error.message);
      throw error;
    }
  }

  /**
   * Run unlimited content only (bypass daily limit)
   */
  async runUnlimitedOnly(options = {}) {
    try {
      logger.info('⚡ Starting unlimited content processing...');

      const allContent = await this.discoverContent();
      if (!allContent) return;

      // Filter only unlimited content
      const unlimitedContent = allContent.filter(content =>
        content.is_exclude_daily_limit === 1 &&
        content.finished !== 1
      );

      if (unlimitedContent.length === 0) {
        logger.warn('⚠️ No unlimited content found');
        return;
      }

      logger.info(`🎯 Found ${unlimitedContent.length} unlimited items`);

      return await this.processContent(unlimitedContent, {
        includeAllContent: true,
        maxTimeMs: options.maxTimeMs,
        bypassDailyLimit: true
      });

    } catch (error) {
      logger.error('❌ Unlimited processing failed', error.message);
      throw error;
    }
  }

  /**
   * Main bot execution
   */
  async run(mode = 'full', options = {}) {
    try {
      this.isRunning = true;
      this.shouldStop = false;

      // Initialize bot
      const initialized = await this.initialize(options);
      if (!initialized) return;

      let results;

      // Run based on mode
      switch (mode) {
        case 'quick':
          results = await this.runQuickSession(options.maxItems);
          break;
        case 'full':
          results = await this.runFullSession(options);
          break;
        case 'unlimited':
          options.bypassDailyLimit = true;
          results = await this.runUnlimitedOnly(options);
          break;
        default:
          throw new Error(`Unknown mode: ${mode}`);
      }

      // Final progress check
      await this.progress.checkProgress();

      // End session and show summary
      const sessionSummary = this.progress.endSession();
      this.progress.logDailySummary();

      logger.success('🎉 Bot execution completed successfully');
      return { sessionSummary, results };

    } catch (error) {
      logger.error('❌ Bot execution failed', error.message);
      
      if (this.isRunning) {
        this.progress.endSession();
      }
      
      throw error;
    } finally {
      this.isRunning = false;
      logger.botStop();
    }
  }

  /**
   * Stop bot execution
   */
  stop() {
    logger.info('🛑 Stopping bot...');
    this.shouldStop = true;
  }

  /**
   * Check bot status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      shouldStop: this.shouldStop,
      sessionSummary: this.progress.getSessionSummary(),
      dailySummary: this.progress.getDailySummary()
    };
  }
}

// Main execution
async function main() {
  const bot = new AmildBot();

  // Handle process signals
  process.on('SIGINT', () => {
    logger.info('📡 Received SIGINT, stopping bot...');
    bot.stop();
  });

  process.on('SIGTERM', () => {
    logger.info('📡 Received SIGTERM, stopping bot...');
    bot.stop();
  });

  try {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const mode = args[0] || 'full';
    const maxItems = parseInt(args[1]) || 20;
    const maxTimeMinutes = parseInt(args[2]) || 60;

    const options = {
      maxItems,
      maxTimeMs: maxTimeMinutes * 60 * 1000
    };

    logger.info(`🎮 Starting bot in ${mode} mode`);

    if (mode === 'unlimited') {
      logger.info('⚡ Unlimited mode: Processing content without daily EXP limit');
    }

    await bot.run(mode, options);

  } catch (error) {
    logger.error('💥 Fatal error', error.message);
    process.exit(1);
  }
}

// Export for use as module
module.exports = AmildBot;

// Run if called directly
if (require.main === module) {
  main();
}
