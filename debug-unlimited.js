/**
 * Debug Unlimited Mode
 * Debug why unlimited mode is not working
 */

const AmildBot = require('./index');
const logger = require('./utils/logger');

async function debugUnlimited() {
  console.log('🐛 Debug Unlimited Mode');
  console.log('=======================');
  
  const bot = new AmildBot();
  
  try {
    // Step 1: Initialize with bypass
    console.log('\n1. Initializing with bypass...');
    const initialized = await bot.initialize({ bypassDailyLimit: true });
    console.log(`   Initialized: ${initialized}`);
    
    if (!initialized) {
      console.log('❌ Initialization failed');
      return;
    }
    
    // Step 2: Check progress with bypass
    console.log('\n2. Checking progress with bypass...');
    const progressCheck = await bot.progress.checkProgress();
    console.log(`   Progress check:`, progressCheck);
    
    const shouldContinue = bot.progress.shouldContinue(true); // bypass = true
    console.log(`   Should continue (with bypass): ${shouldContinue}`);
    
    // Step 3: Discover content
    console.log('\n3. Discovering content...');
    const allContent = await bot.discoverContent();
    console.log(`   Total content found: ${allContent ? allContent.length : 0}`);
    
    if (allContent && allContent.length > 0) {
      // Step 4: Filter unlimited content
      console.log('\n4. Filtering unlimited content...');
      const unlimitedContent = allContent.filter(content => 
        content.is_exclude_daily_limit === 1 && 
        content.finished !== 1
      );
      
      console.log(`   Unlimited content: ${unlimitedContent.length}`);
      
      if (unlimitedContent.length > 0) {
        console.log('   First few unlimited items:');
        unlimitedContent.slice(0, 3).forEach((item, index) => {
          console.log(`     ${index + 1}. ${item.title} (${item.format}, ${item.point} points)`);
        });
        
        // Step 5: Test processing one item
        console.log('\n5. Testing processing one item...');
        const testItem = unlimitedContent[0];
        
        console.log(`   Processing: ${testItem.title}`);
        console.log(`   Format: ${testItem.format}`);
        console.log(`   Points: ${testItem.point}`);
        console.log(`   Campaign: ${testItem.campaign_string_id}`);
        console.log(`   Exclude daily limit: ${testItem.is_exclude_daily_limit}`);
        console.log(`   Finished: ${testItem.finished}`);
        
        // Try to process based on format
        if (testItem.format === 'article') {
          console.log('   📄 Processing as article...');
          try {
            const result = await bot.articleProcessor.processArticles([testItem]);
            console.log('   ✅ Article processing result:', result);
          } catch (error) {
            console.log('   ❌ Article processing failed:', error.message);
          }
        } else if (testItem.format === 'carousel') {
          console.log('   🎠 Processing as carousel...');
          try {
            const result = await bot.carouselProcessor.processCarousels([testItem]);
            console.log('   ✅ Carousel processing result:', result);
          } catch (error) {
            console.log('   ❌ Carousel processing failed:', error.message);
          }
        } else if (testItem.format === 'video') {
          console.log('   🎥 Processing as video...');
          try {
            const result = await bot.videoProcessor.processVideos([testItem]);
            console.log('   ✅ Video processing result:', result);
          } catch (error) {
            console.log('   ❌ Video processing failed:', error.message);
          }
        }
      } else {
        console.log('   ❌ No unlimited content found');
      }
    } else {
      console.log('   ❌ No content discovered');
    }
    
    console.log('\n🎉 Debug completed');
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
    console.log('Stack:', error.stack);
  }
}

debugUnlimited();
