/**
 * Test Real Point Earning
 * Test if bot actually earns real points
 */

const axios = require('axios');

const config_file = require('./config');
const WORKING_TOKEN = config_file.AUTH.WORKING_TOKEN;

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
  }
};

async function testRealPoints() {
  console.log('💰 Testing Real Point Earning');
  console.log('=============================');
  
  try {
    // Step 1: Check initial points
    console.log('\n1. Checking initial points...');
    
    const initialPointsResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/ub-point`, {
      content_type: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    const initialPoints = initialPointsResponse.data.data.point;
    console.log(`✅ Initial points: ${initialPoints}`);
    
    // Step 2: Get unlimited content
    console.log('\n2. Getting unlimited content...');
    
    const contentResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
      is_regular: 1,
      campaign: '',
      category: '',
      sub_category: 'music',
      is_branded: 1,
      format: '',
      is_highlight: '',
      pinned: '',
      page: 0,
      length: 10,
      is_engagement: '',
      string_id: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    const allContent = contentResponse.data.data.items;
    const unlimitedContent = allContent.filter(item => 
      item.is_exclude_daily_limit === 1 && 
      item.finished !== 1
    );
    
    console.log(`✅ Found ${unlimitedContent.length} unlimited items`);
    
    if (unlimitedContent.length === 0) {
      console.log('❌ No unlimited content available');
      return;
    }
    
    // Step 3: Process one item manually
    const testItem = unlimitedContent[0];
    console.log(`\n3. Processing: ${testItem.title}`);
    console.log(`   Format: ${testItem.format}`);
    console.log(`   Points: ${testItem.point}`);
    console.log(`   Finished: ${testItem.finished}`);
    console.log(`   Unlimited: ${testItem.is_exclude_daily_limit}`);
    
    // Step 4: Track article view
    console.log('\n4. Tracking article view...');
    
    const viewResponse = await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
      user_id: false,
      event: 'general_event',
      campaign: testItem.campaign_string_id,
      event_category: 'on view article',
      event_action: 'on view article',
      event_label: testItem.title,
      creative: testItem.href,
      content_id: parseInt(testItem.id)
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    console.log(`✅ View tracking: ${viewResponse.data.code === 200 ? 'SUCCESS' : 'FAILED'}`);
    
    // Step 5: Track scroll events
    console.log('\n5. Tracking scroll events...');
    
    for (let i = 0; i < 3; i++) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const scrollResponse = await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
        user_id: false,
        event: 'scroll_depth_article',
        campaign: testItem.campaign_string_id,
        event_category: 'scroll_depth_article',
        event_action: 'content',
        event_label: 'article',
        creative: testItem.href,
        content_id: parseInt(testItem.id)
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      console.log(`   Scroll ${i + 1}: ${scrollResponse.data.code === 200 ? 'SUCCESS' : 'FAILED'}`);
    }
    
    // Step 6: Track completion
    console.log('\n6. Tracking completion...');
    
    const completionResponse = await axios.post(`${config.API.BASE_URL}/api/fs/interaction-user`, {
      user_id: false,
      event: 'general_event',
      campaign: testItem.campaign_string_id,
      event_category: 'page_reached_finish_on_read_article',
      event_action: 'simple engagement',
      event_label: testItem.title,
      creative: testItem.href,
      content_id: parseInt(testItem.id)
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    console.log(`✅ Completion tracking: ${completionResponse.data.code === 200 ? 'SUCCESS' : 'FAILED'}`);
    
    // Step 7: Wait and check points again
    console.log('\n7. Waiting 5 seconds and checking points...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const finalPointsResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/ub-point`, {
      content_type: ''
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': WORKING_TOKEN,
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    const finalPoints = finalPointsResponse.data.data.point;
    const pointsEarned = finalPoints - initialPoints;
    
    console.log(`✅ Final points: ${finalPoints}`);
    console.log(`💰 Points earned: ${pointsEarned}`);
    
    if (pointsEarned > 0) {
      console.log('🎉 SUCCESS! Real points were earned!');
    } else {
      console.log('❌ No points earned. Possible reasons:');
      console.log('   - Content already processed');
      console.log('   - Missing required tracking sequence');
      console.log('   - Points delayed (check again later)');
      
      // Check if content is now marked as finished
      console.log('\n8. Checking if content is now finished...');
      
      const updatedContentResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
        is_regular: 1,
        campaign: '',
        category: '',
        sub_category: 'music',
        is_branded: 1,
        format: '',
        is_highlight: '',
        pinned: '',
        page: 0,
        length: 10,
        is_engagement: '',
        string_id: ''
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': WORKING_TOKEN,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      const updatedItem = updatedContentResponse.data.data.items.find(item => item.id === testItem.id);
      if (updatedItem) {
        console.log(`   Content finished status: ${updatedItem.finished}`);
        if (updatedItem.finished === 1) {
          console.log('   ✅ Content marked as finished - processing was successful!');
        }
      }
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log('Error data:', error.response.data);
    }
  }
}

testRealPoints();
