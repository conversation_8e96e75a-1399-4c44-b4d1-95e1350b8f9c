/**
 * Bypass Authentication Script
 * Use existing session/cookies to bypass complex AllAccess flow
 */

const axios = require('axios');

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
  },
  AUTH: {
    NAME: 'amild',
    SECRET_KEY: '8Q1kUKMlUZUsCntnclnPF+DDQ+k1HY0b1UNGesHK',
    APP_KEY: '629DE67FBE4A67B65541',
    DEVICE_ID: 'meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r',
    DEVICE_TYPE: 'website'
  }
};

// Try different tokens that might be working
const POTENTIAL_TOKENS = [
  'dJJH2C7WSUZ9Cg3gOqrsPskXQWyzCWbG', // From your AllAccess request
  'r5rMPe5AYTQP2HuftKv4lu0p9c6JpkEa'  // From previous endpoints
];

async function bypassAuth() {
  console.log('🔓 Attempting Authentication Bypass');
  console.log('===================================');
  
  // Method 1: Try existing tokens
  console.log('1. Testing existing tokens...');
  
  for (const token of POTENTIAL_TOKENS) {
    try {
      console.log(`\n   Testing token: ${token.substring(0, 10)}...`);
      
      // Test login status
      const loginResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/login-status`, {}, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': token,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      console.log(`   Login status response: ${loginResponse.data.code}`);
      
      if (loginResponse.data.code === 200) {
        const isLoggedIn = loginResponse.data.data.data.login_status;
        console.log(`   Login status: ${isLoggedIn}`);
        
        if (isLoggedIn) {
          console.log('   ✅ Token is active! Testing profile...');
          
          // Test profile
          const profileResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/get-profile`, {}, {
            headers: {
              'Content-Type': 'application/json',
              'Standardized-Token': token,
              'User-Agent': config.API.USER_AGENT,
              'Origin': config.API.ORIGIN,
              'Accept': '*/*'
            }
          });
          
          if (profileResponse.data.code === 200) {
            const profile = profileResponse.data.data.data;
            console.log(`   ✅ Profile loaded: ${profile.fullname} (${profile.email})`);
            console.log(`   ✅ WORKING TOKEN FOUND: ${token}`);
            
            // Test content access
            console.log('\n   Testing content access...');
            const contentResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
              is_regular: 1,
              campaign: '',
              category: '',
              sub_category: 'music',
              is_branded: 1,
              format: '',
              is_highlight: '',
              pinned: '',
              page: 0,
              length: 5,
              is_engagement: '',
              string_id: ''
            }, {
              headers: {
                'Content-Type': 'application/json',
                'Standardized-Token': token,
                'User-Agent': config.API.USER_AGENT,
                'Origin': config.API.ORIGIN,
                'Accept': '*/*'
              }
            });
            
            if (contentResponse.data.code === 200) {
              console.log(`   ✅ Content access successful: ${contentResponse.data.data.items.length} items`);
              console.log('\n🎉 BYPASS SUCCESSFUL!');
              console.log(`🔑 Working token: ${token}`);
              console.log('💡 Update config.js with this token for immediate use');
              return token;
            }
          }
        }
      }
    } catch (error) {
      console.log(`   ❌ Token failed: ${error.message}`);
    }
  }
  
  // Method 2: Generate new token and test immediately
  console.log('\n2. Generating fresh token...');
  
  try {
    const tokenResponse = await axios.post(`${config.API.BASE_URL}/api/token/get`, {
      name: config.AUTH.NAME,
      secret_key: config.AUTH.SECRET_KEY,
      device_type: config.AUTH.DEVICE_TYPE,
      app_key: config.AUTH.APP_KEY,
      device_id: config.AUTH.DEVICE_ID
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (tokenResponse.data.code === 200) {
      const newToken = tokenResponse.data.data.token.token_code;
      console.log(`✅ New token generated: ${newToken.substring(0, 20)}...`);
      
      // Test immediately without waiting
      const quickTest = await axios.post(`${config.API.BASE_URL}/api/fs/user/get-profile`, {}, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': newToken,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      if (quickTest.data.code === 200) {
        console.log('✅ New token works immediately!');
        console.log(`🔑 Fresh working token: ${newToken}`);
        return newToken;
      } else {
        console.log('⚠️ New token requires activation');
      }
    }
  } catch (error) {
    console.log(`❌ Token generation failed: ${error.message}`);
  }
  
  // Method 3: Try with different device IDs
  console.log('\n3. Trying alternative device IDs...');
  
  const alternativeDeviceIds = [
    'LJWWbJfnxXPfpfryv83GJf7rluoPdBTkCctL', // Original
    'meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r', // Current
    'abcd1234efgh5678ijkl9012mnop3456qrst', // Random
    'test1234test5678test9012test3456test'   // Test
  ];
  
  for (const deviceId of alternativeDeviceIds) {
    try {
      console.log(`\n   Testing device ID: ${deviceId.substring(0, 10)}...`);
      
      const altTokenResponse = await axios.post(`${config.API.BASE_URL}/api/token/get`, {
        name: config.AUTH.NAME,
        secret_key: config.AUTH.SECRET_KEY,
        device_type: config.AUTH.DEVICE_TYPE,
        app_key: config.AUTH.APP_KEY,
        device_id: deviceId
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      if (altTokenResponse.data.code === 200) {
        const altToken = altTokenResponse.data.data.token.token_code;
        console.log(`   ✅ Token generated: ${altToken.substring(0, 15)}...`);
        
        // Quick profile test
        const altProfileTest = await axios.post(`${config.API.BASE_URL}/api/fs/user/get-profile`, {}, {
          headers: {
            'Content-Type': 'application/json',
            'Standardized-Token': altToken,
            'User-Agent': config.API.USER_AGENT,
            'Origin': config.API.ORIGIN,
            'Accept': '*/*'
          }
        });
        
        if (altProfileTest.data.code === 200) {
          console.log('   ✅ Alternative device ID works!');
          console.log(`   🔑 Working token: ${altToken}`);
          console.log(`   📱 Working device ID: ${deviceId}`);
          return altToken;
        }
      }
    } catch (error) {
      console.log(`   ❌ Device ID failed: ${error.message}`);
    }
  }
  
  console.log('\n❌ All bypass methods failed');
  console.log('💡 Manual authentication may be required');
  console.log('🔗 Try running: npm run test-allaccess');
  
  return null;
}

// Run bypass
bypassAuth().then(token => {
  if (token) {
    console.log('\n🎉 SUCCESS! Use this token in your bot:');
    console.log(`TOKEN: ${token}`);
  } else {
    console.log('\n❌ Bypass failed. Manual authentication required.');
  }
}).catch(error => {
  console.log('💥 Bypass script failed:', error.message);
});
