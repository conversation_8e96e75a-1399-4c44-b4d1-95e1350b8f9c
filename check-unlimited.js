/**
 * Check Unlimited Content
 * Check content that can be processed without daily limit
 */

const axios = require('axios');

const config_file = require('./config');
const WORKING_TOKEN = config_file.AUTH.WORKING_TOKEN;

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
  }
};

const categories = ['music', 'daily-life', 'travel', 'lifestyle', 'art', 'sustainability'];

async function checkUnlimitedContent() {
  console.log('🔍 Checking Unlimited Content');
  console.log('=============================');
  
  let totalUnlimited = 0;
  let totalPoints = 0;
  let unlimitedByCategory = {};
  
  try {
    for (const category of categories) {
      console.log(`\n📂 Checking category: ${category}`);
      
      let categoryUnlimited = 0;
      let categoryPoints = 0;
      let page = 0;
      let hasMorePages = true;
      
      while (hasMorePages && page < 10) { // Safety limit
        try {
          const response = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
            is_regular: 1,
            campaign: '',
            category: '',
            sub_category: category,
            is_branded: 1,
            format: '',
            is_highlight: '',
            pinned: '',
            page: page,
            length: 10,
            is_engagement: '',
            string_id: ''
          }, {
            headers: {
              'Content-Type': 'application/json',
              'Standardized-Token': WORKING_TOKEN,
              'User-Agent': config.API.USER_AGENT,
              'Origin': config.API.ORIGIN,
              'Accept': '*/*'
            }
          });
          
          if (response.data.code === 200) {
            const items = response.data.data.items;
            const paging = response.data.data.paging;
            
            // Filter unlimited content
            const unlimitedItems = items.filter(item => 
              item.is_exclude_daily_limit === 1 && 
              item.finished !== 1
            );
            
            categoryUnlimited += unlimitedItems.length;
            categoryPoints += unlimitedItems.reduce((sum, item) => sum + (item.point || 0), 0);
            
            console.log(`   Page ${page}: ${unlimitedItems.length}/${items.length} unlimited items`);
            
            // Show some examples
            unlimitedItems.slice(0, 2).forEach(item => {
              console.log(`     • ${item.title} (${item.format}, ${item.point} points, ${item.campaign_string_id})`);
            });
            
            // Check if more pages
            hasMorePages = paging.currPage < paging.lastPage;
            page++;
            
            // Small delay
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            console.log(`   ❌ Failed to fetch page ${page}`);
            break;
          }
        } catch (error) {
          console.log(`   ❌ Error on page ${page}: ${error.message}`);
          break;
        }
      }
      
      unlimitedByCategory[category] = {
        count: categoryUnlimited,
        points: categoryPoints
      };
      
      totalUnlimited += categoryUnlimited;
      totalPoints += categoryPoints;
      
      console.log(`   📊 ${category}: ${categoryUnlimited} unlimited items (${categoryPoints} points)`);
    }
    
    // Summary
    console.log('\n📊 UNLIMITED CONTENT SUMMARY');
    console.log('============================');
    console.log(`Total Unlimited Items: ${totalUnlimited}`);
    console.log(`Total Potential Points: ${totalPoints}`);
    console.log('');
    
    // By category breakdown
    console.log('📋 By Category:');
    Object.entries(unlimitedByCategory).forEach(([cat, data]) => {
      if (data.count > 0) {
        console.log(`   ${cat}: ${data.count} items (${data.points} points)`);
      }
    });
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (totalUnlimited > 0) {
      console.log(`✅ Found ${totalUnlimited} unlimited items worth ${totalPoints} points!`);
      console.log('🚀 You can process these without affecting daily EXP limit');
      console.log('');
      console.log('🎮 Commands to run:');
      console.log('   npm run force-unlimited    # Process only unlimited content');
      console.log('   npm start unlimited        # Process unlimited content');
    } else {
      console.log('❌ No unlimited content found');
      console.log('⏰ Wait for daily reset or check again later');
    }
    
    // Check high-value campaigns
    console.log('\n🎯 Checking high-value campaigns...');
    
    const highValueCampaigns = ['gac-2024', 'inspiraksi2025', 'go-ahead-music'];
    
    for (const campaign of highValueCampaigns) {
      try {
        const campaignResponse = await axios.post(`${config.API.BASE_URL}/api/fs/feed/list`, {
          is_regular: 1,
          campaign: campaign,
          category: '',
          sub_category: '',
          is_branded: 1,
          format: '',
          is_highlight: '',
          pinned: '',
          page: 0,
          length: 10,
          is_engagement: '',
          string_id: ''
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Standardized-Token': WORKING_TOKEN,
            'User-Agent': config.API.USER_AGENT,
            'Origin': config.API.ORIGIN,
            'Accept': '*/*'
          }
        });
        
        if (campaignResponse.data.code === 200) {
          const items = campaignResponse.data.items || [];
          const unlimitedCampaignItems = items.filter(item => 
            item.is_exclude_daily_limit === 1 && 
            item.finished !== 1
          );
          
          if (unlimitedCampaignItems.length > 0) {
            console.log(`   🎯 ${campaign}: ${unlimitedCampaignItems.length} unlimited items`);
            unlimitedCampaignItems.slice(0, 2).forEach(item => {
              console.log(`     • ${item.title} (${item.point} points)`);
            });
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.log(`   ❌ ${campaign}: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.log('❌ Check failed:', error.message);
  }
}

checkUnlimitedContent();
