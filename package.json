{"name": "amild-auto-farming-bot", "version": "1.0.0", "description": "Automated point farming bot for Amild.id platform", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node test.js", "test-auth": "node test-auth.js", "quick-test": "node quick-test.js", "test-req-url": "node test-req-url.js", "test-allaccess": "node test-allaccess-flow.js", "test-working": "node test-working-token.js", "check-unlimited": "node check-unlimited.js", "debug-unlimited": "node debug-unlimited.js", "test-real-points": "node test-real-points.js", "bypass": "node bypass-auth.js", "debug": "node --inspect index.js"}, "keywords": ["amild", "bot", "automation", "farming", "points"], "author": "<PERSON><PERSON><PERSON> Bo<PERSON> Developer", "license": "MIT", "dependencies": {"axios": "^1.6.0", "colors": "^1.4.0", "moment": "^2.29.4", "fs-extra": "^11.1.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}