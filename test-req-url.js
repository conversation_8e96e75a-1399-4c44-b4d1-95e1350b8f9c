/**
 * Test Request URL Endpoint
 * Test the new req-url endpoint specifically
 */

const axios = require('axios');

const config = {
  API: {
    BASE_URL: 'https://api.oneux.id',
    ORIGIN: 'https://amild.id',
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
  },
  AUTH: {
    NAME: 'amild',
    SECRET_KEY: '8Q1kUKMlUZUsCntnclnPF+DDQ+k1HY0b1UNGesHK',
    APP_KEY: '629DE67FBE4A67B65541',
    DEVICE_ID: 'meFlnlubUjKZRxemlKmI9LJ0h0Lkic9x0W7r',
    DEVICE_TYPE: 'website'
  }
};

async function testReqUrl() {
  console.log('🧪 Testing Request URL Endpoint');
  console.log('================================');
  
  try {
    // Step 1: Generate Token
    console.log('1. Generating token...');
    
    const tokenResponse = await axios.post(`${config.API.BASE_URL}/api/token/get`, {
      name: config.AUTH.NAME,
      secret_key: config.AUTH.SECRET_KEY,
      device_type: config.AUTH.DEVICE_TYPE,
      app_key: config.AUTH.APP_KEY,
      device_id: config.AUTH.DEVICE_ID
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': config.API.USER_AGENT,
        'Origin': config.API.ORIGIN,
        'Accept': '*/*'
      }
    });
    
    if (tokenResponse.data.code !== 200) {
      throw new Error('Token generation failed');
    }
    
    const token = tokenResponse.data.data.token.token_code;
    console.log(`✅ Token: ${token.substring(0, 20)}...`);
    
    // Step 2: Test Request URL with exact data from your example
    console.log('\n2. Testing Request URL endpoint...');
    
    const reqUrlData = {
      ga: "2.62775660.1964620225.1752408888-214340915.1752408888",
      gl: "1*hmudcy*_ga*MjE0MzQwOTE1LjE3NTI0MDg4ODg.*_ga_Q0M8VSMG8Z*czE3NTI0MDg4ODkkbzEkZzEkdDE3NTI0MDg5MDgkajQxJGwwJGgw",
      detail_page: "https://amild.id/br",
      first_page: "login",
      platform: "amild x all",
      auth_data: "",
      ref: "",
      redirect_url: "https://amild.id/allaccess-loading",
      utm: ""
    };
    
    console.log('Request data:', JSON.stringify(reqUrlData, null, 2));
    
    const reqUrlResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/req-url`, reqUrlData, {
      headers: {
        'Content-Type': 'application/json',
        'Standardized-Token': token,
        'User-Agent': config.API.USER_AGENT,
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Accept-Language': 'en-US,en;q=0.9',
        'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Accept': '*/*',
        'Origin': config.API.ORIGIN,
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Referer': 'https://amild.id/',
        'Accept-Encoding': 'gzip, deflate, br',
        'Priority': 'u=1, i'
      }
    });
    
    console.log('\n✅ Request URL Response:');
    console.log('Status:', reqUrlResponse.status);
    console.log('Headers:', reqUrlResponse.headers);
    console.log('Data:', JSON.stringify(reqUrlResponse.data, null, 2));
    
    if (reqUrlResponse.data.code === 200) {
      const urlData = reqUrlResponse.data.data.data;
      console.log('\n📋 Parsed Response:');
      console.log(`✅ URL: ${urlData.url.substring(0, 100)}...`);
      console.log(`✅ Platform: ${urlData.platform}`);
      console.log(`✅ Visit AllAccess: ${urlData.visit_allaccess}`);
      
      // Try to analyze the URL
      const url = new URL(urlData.url);
      console.log('\n🔍 URL Analysis:');
      console.log(`   Host: ${url.host}`);
      console.log(`   Pathname: ${url.pathname}`);
      console.log(`   Auth Profile Length: ${url.searchParams.get('auth_profile')?.length || 0}`);
      console.log(`   GA: ${url.searchParams.get('_ga')}`);
      console.log(`   GL: ${url.searchParams.get('_gl')}`);
    }
    
    // Step 3: Test with different parameters
    console.log('\n3. Testing with minimal parameters...');
    
    const minimalData = {
      ga: "",
      gl: "",
      detail_page: "https://amild.id/br",
      first_page: "login",
      platform: "amild x all",
      auth_data: "",
      ref: "",
      redirect_url: "https://amild.id/allaccess-loading",
      utm: ""
    };
    
    try {
      const minimalResponse = await axios.post(`${config.API.BASE_URL}/api/fs/user/req-url`, minimalData, {
        headers: {
          'Content-Type': 'application/json',
          'Standardized-Token': token,
          'User-Agent': config.API.USER_AGENT,
          'Origin': config.API.ORIGIN,
          'Accept': '*/*'
        }
      });
      
      console.log('✅ Minimal request successful');
      console.log('Response code:', minimalResponse.data.code);
      
      if (minimalResponse.data.code === 200) {
        const minimalUrlData = minimalResponse.data.data.data;
        console.log(`✅ Minimal URL: ${minimalUrlData.url.substring(0, 100)}...`);
      }
    } catch (minimalError) {
      console.log('❌ Minimal request failed:', minimalError.response?.data || minimalError.message);
    }
    
    console.log('\n🎉 Request URL test completed!');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response) {
      console.log('Error status:', error.response.status);
      console.log('Error data:', error.response.data);
    }
  }
}

testReqUrl();
